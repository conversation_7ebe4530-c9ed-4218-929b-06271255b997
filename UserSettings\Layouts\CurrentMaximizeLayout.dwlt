%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 3}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 36
    width: 1920
    height: 933
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 1
  controlID: 100
  draggingID: 0
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 706
    width: 966
    height: 280
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders:
    - Assets
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 67
  m_LastFolders:
  - Assets
  m_LastFoldersGridSize: 67
  m_LastProjectPath: F:\Match2D
  m_LockTracker:
    m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 79}
    m_SelectedIDs: 1c1b0100
    m_LastClickedID: 72476
    m_ExpandedIDs: 000000009e0101001c1b01001e1b0100201b0100221b0100241b0100261b0100281b01002a1b01002c1b01002e1b0100301b0100321b0100341b0100361b0100381b01003a1b01003c1b01003e1b0100401b0100421b0100441b0100461b0100481b01004a1b01004c1b01004e1b0100501b0100521b0100541b0100561b0100581b01005a1b01005c1b01005e1b0100601b0100621b0100641b0100661b0100681b01006a1b01006c1b01006e1b0100701b0100721b0100741b0100761b0100781b01007a1b01007c1b010000ca9a3bffffff7f
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 000000009e0101001c1b01001e1b0100201b0100221b0100241b0100261b0100281b01002a1b01002c1b01002e1b0100301b0100321b0100341b0100361b0100381b01003a1b01003c1b01003e1b0100401b0100421b0100441b0100461b0100481b01004a1b01004c1b01004e1b0100501b0100521b0100541b0100561b0100581b01005a1b01005c1b01005e1b0100601b0100621b0100641b0100661b0100681b01006a1b01006c1b01006e1b0100701b0100721b0100741b0100761b0100781b01007a1b01007c1b0100
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 
    m_LastClickedInstanceID: 0
    m_HadKeyboardFocusLastEvent: 0
    m_ExpandedInstanceIDs: c6230000fa1e0100d22601001cc41600c6942a00fc760100064b00009eb300009ccf000032d0000002c101002afb02002efb020022fb020036fb02003afb020082be00005ca7000062a7000068a700006ea7000074a700007aa70000fa95000052b503003a96020036a8000038130200d48c0000e0ef0000e08f0000428f0000da8f0000e68f0000ea8f0000e88f000068cd00008ec70d00fe8d0000c68b0000decd010070ab03003ea901006ca40e00b8360300c2c70200eced020002a2020054e60800ccb20200dabb0000be680d00249a0100fabd0000e08f06005ec00000580e0200380e02007e4a0100804a01009a28020028290200e8d800009c920000a29200009a9200009892000062bb000084d600004a9d0600b09d0600d2ba000084ba0000c8a00200cc951300de000100e4b4000058b30000ea7601000abd01002cb00000e2af00006ab0000064b000006ec7000096b90100f44002006c73020088880200508e0200c6d30000f497000004a4000050ff0100a8e40100dc4801003656010090e70100b64a00000ee600006e430000f628010024090100baa30100d6a30100defd0300eafb0e00ecfb0e001e371200fe5a0200a8340100d059020064350100129d0100c20a03009a3901008cf80100362801002ee008002c700100ee4f010040140100182f0100d66001002c140100e6320100b82e010052300100e45801001419040000000000fc71010000d20100a84b0100e87a01001ef601008e1e0100f61f0100ba1f01008a1f0100721f01006a1f0100da3c0100ce3c01003e3c010084da000000050000ae020000c24e010016dd000038d200000ad70000feda0000b8f3000084f3000090e3000004f8000002ea000008320100e6ee00001a13010000ef0000201301001e13010052f200008c120100960501006e3c0100202b0100762d010086020100940f0100
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 67
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 266
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 4}
  - {fileID: 22}
  - {fileID: 24}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1920
    height: 933
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 0
  controlID: 101
  draggingID: 0
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 5}
  - {fileID: 14}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 967
    height: 933
  m_MinSize: {x: 200, y: 100}
  m_MaxSize: {x: 16192, y: 16192}
  vertical: 1
  controlID: 55
  draggingID: 0
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 6}
  - {fileID: 8}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 967
    height: 627
  m_MinSize: {x: 200, y: 50}
  m_MaxSize: {x: 16192, y: 8096}
  vertical: 0
  controlID: 56
  draggingID: 0
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneHierarchyWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 287
    height: 627
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 7}
  m_Panes:
  - {fileID: 7}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: 7966133145522015247, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Hierarchy\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 79
    width: 286
    height: 601
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SceneHierarchy:
    m_TreeViewState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: e2faffff
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_TrimLeadingAndTrailingWhitespace: 0
        m_ClientGUIView: {fileID: 6}
      m_SearchString: 
    m_ExpandedScenes: []
    m_CurrenRootInstanceID: 0
    m_LockTracker:
      m_IsLocked: 0
    m_CurrentSortingName: TransformSorting
  m_WindowGUID: 4c969a2b90040154d917609493e03593
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 287
    y: 0
    width: 680
    height: 627
  m_MinSize: {x: 202, y: 226}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 9}
  m_Panes:
  - {fileID: 9}
  - {fileID: 10}
  - {fileID: 11}
  - {fileID: 12}
  - {fileID: 13}
  m_Selected: 0
  m_LastSelected: 2
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 2593428753322112591, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Scene\u200B"
  m_Pos:
    serializedVersion: 2
    x: 287
    y: 79
    width: 678
    height: 601
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: Tool Settings
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-171.0,"y":-26.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -171, y: -26}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-toolbar
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-291.0,"y":-206.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -291, y: -206}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 0
      id: unity-search-toolbar
      index: 2
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":-24.0,"y":24.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: -24, y: 24}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-transform-toolbar
      index: 2
      contents: '{"m_Layout":2,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":49.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 49}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: Orientation
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":67.5,"y":-70.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":2,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 67.5, y: -70}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 2
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-grid-and-snap-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-145.0,"y":149.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -145, y: 149}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Light Settings
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Camera
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: -256, y: -161}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Constraints
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Collisions
      index: 5
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Navmesh Display
      index: 4
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Agent Display
      index: 5
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Obstacle Display
      index: 6
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Occlusion Culling
      index: 6
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Physics Debugger
      index: 7
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Scene Visibility
      index: 8
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Scene View/Component Tools
      index: 10
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 0
      id: Scene View/Particles
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-231.0009765625,"y":-202.0009765625},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -231.00098, y: -202.00098}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 0
      id: Scene View/Open Tile Palette
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: Scene View/Tilemap Focus
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":49.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 49}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Model
      index: 11
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Event Tester
      index: 14
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/UI Particles
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: AINavigationOverlay
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-164.0009765625,"y":-130.0009765625},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -164.00098, y: -130.00098}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Timeline Control
      index: 17
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: APV Overlay
      index: 9
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Cinemachine Tool Settings
      index: 14
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/TrailRenderer
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":true,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.SceneViewCameraOverlay
      index: 16
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 35}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-camera-mode-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":49.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 49}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Terrain Tools
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Masks
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 0
      id: Scene View/Lighting Visualization Colors
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 1
      id: Overlays/OverlayMenu
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":49.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 49}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tile Palette Clipboard
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tile Palette Brush Pick
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Path
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: SceneView/CamerasOverlay
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/PBR Validation Settings
      index: 16
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: unity-spline-inspector
      index: 20
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Sprite Swap
      index: 18
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":24.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 24}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    m_ContainerData:
    - containerId: overlay-toolbar__top
      scrollOffset: 0
    - containerId: overlay-toolbar__left
      scrollOffset: 0
    - containerId: overlay-container--left
      scrollOffset: 0
    - containerId: overlay-container--right
      scrollOffset: 0
    - containerId: overlay-toolbar__right
      scrollOffset: 0
    - containerId: overlay-toolbar__bottom
      scrollOffset: 0
    - containerId: Floating
      scrollOffset: 0
    m_OverlaysVisible: 1
  m_WindowGUID: cc27987af1a868c49b0894db9c0f5429
  m_Gizmos: 1
  m_OverrideSceneCullingMask: 6917529027641081856
  m_SceneIsLit: 1
  m_SceneLighting: 1
  m_2DMode: 1
  m_isRotationLocked: 0
  m_PlayAudio: 0
  m_AudioPlay: 0
  m_DebugDrawModesUseInteractiveLightBakingData: 0
  m_Position:
    m_Target: {x: 2.1932917, y: 1.4624709, z: -0.010800242}
    speed: 2
    m_Value: {x: 2.1932917, y: 1.4624709, z: -0.010800242}
  m_RenderMode: 0
  m_CameraMode:
    drawMode: 0
    name: Shaded
    section: Shading Mode
  m_ValidateTrueMetals: 0
  m_DoValidateTrueMetals: 0
  m_SceneViewState:
    m_AlwaysRefresh: 1
    showFog: 0
    showSkybox: 0
    showFlares: 0
    showImageEffects: 1
    showParticleSystems: 1
    showVisualEffectGraphs: 1
    m_FxEnabled: 1
  m_Grid:
    xGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    yGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    zGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    m_ShowGrid: 0
    m_GridAxis: 1
    m_gridOpacity: 0.5
  m_Rotation:
    m_Target: {x: 0, y: 0, z: 0, w: 1}
    speed: 2
    m_Value: {x: 0, y: 0, z: 0, w: 1}
  m_Size:
    m_Target: 16.719881
    speed: 2
    m_Value: 16.719881
  m_Ortho:
    m_Target: 1
    speed: 2
    m_Value: 1
  m_CameraSettings:
    m_Speed: 2
    m_SpeedNormalized: 1
    m_SpeedMin: 0.001
    m_SpeedMax: 2
    m_EasingEnabled: 1
    m_EasingDuration: 0.4
    m_AccelerationEnabled: 1
    m_FieldOfViewHorizontalOrVertical: 60
    m_NearClip: 0.02
    m_FarClip: 10000
    m_DynamicClip: 0
    m_OcclusionCulling: 1
  m_LastSceneViewRotation: {x: -0.08717229, y: 0.89959055, z: -0.21045254, w: -0.3726226}
  m_LastSceneViewOrtho: 0
  m_Viewpoint:
    m_SceneView: {fileID: 9}
    m_CameraOverscanSettings:
      m_Opacity: 50
      m_Scale: 1
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_SceneVisActive: 1
  m_LastLockedObject: {fileID: 0}
  m_LastDebugDrawMode:
    drawMode: 35
    name: Contributors / Receivers
    section: Lighting
  m_ViewIsLockedToObject: 0
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cd0276e3a65ecfd43904ccfe7027d92d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: VFX_Bubbles
    m_Image: {fileID: 2800000, guid: dbf0a247eda3c834b86a04d35558ba9f, type: 3}
    m_Tooltip: 
    m_TextWithWhitespace: "VFX_Bubbles\u200B"
  m_Pos:
    serializedVersion: 2
    x: 268
    y: 79
    width: 1186
    height: 579
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_DisplayedResource: {fileID: 8926484042661614527, guid: 3019faea6fd6a3a4d853afc2c3284a70, type: 2}
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12914, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Animator
    m_Image: {fileID: -1673928668082335149, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Animator\u200B"
  m_Pos:
    serializedVersion: 2
    x: 237
    y: 79
    width: 611
    height: 601
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ViewTransforms:
    m_KeySerializationHelper:
    - {fileID: -91450862855426347, guid: f09de2b80ce924c2a8254670c724b0f6, type: 2}
    - {fileID: -4032332726532279086, guid: 04f4da536bfcf7e479e9260fa0ac7a9e, type: 2}
    - {fileID: -7308412818149504184, guid: a02a082f7fdc5334aa52840ec88a67cd, type: 2}
    - {fileID: -5932246347793798608, guid: 22431d13fcb8eee4b8dbe547c1f4e341, type: 2}
    - {fileID: -3943141088868255448, guid: 588a4fad6377a484f9f0dc544d65b527, type: 2}
    - {fileID: 482663509705705761, guid: 1cd78c133df042048b88270ca267412d, type: 2}
    m_ValueSerializationHelper:
    - e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.91299224
      e01: 0
      e02: 0
      e03: 52.71608
      e10: 0
      e11: 0.91299224
      e12: 0
      e13: 135.05116
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.811907
      e01: 0
      e02: 0
      e03: -64.460175
      e10: 0
      e11: 0.811907
      e12: 0
      e13: 304.78137
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.93369305
      e01: 0
      e02: 0
      e03: -137.87683
      e10: 0
      e11: 0.93369305
      e12: 0
      e13: 382.43433
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.46421054
      e01: 0
      e02: 0
      e03: -8.210541
      e10: 0
      e11: 0.46421054
      e12: 0
      e13: 241.5421
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.46421054
      e01: 0
      e02: 0
      e03: -8.210556
      e10: 0
      e11: 0.46421054
      e12: 0
      e13: 241.5421
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_PreviewAnimator: {fileID: 0}
  m_AnimatorController: {fileID: 9100000, guid: a02a082f7fdc5334aa52840ec88a67cd, type: 2}
  m_BreadCrumbs:
  - m_Target: {fileID: -7308412818149504184, guid: a02a082f7fdc5334aa52840ec88a67cd, type: 2}
    m_ScrollPosition: {x: 0, y: 0}
  stateMachineGraph: {fileID: 0}
  stateMachineGraphGUI: {fileID: 0}
  blendTreeGraph: {fileID: 0}
  blendTreeGraphGUI: {fileID: 0}
  m_AutoLiveLink: 1
  m_MiniTool: 0
  m_LockTracker:
    m_IsLocked: 0
  m_CurrentEditor: 1
  m_LayerEditor:
    m_SelectedLayerIndex: 0
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 924ffcbe75518854f97b48776d0f1939, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: HealthBarShader
    m_Image: {fileID: 2800000, guid: 7129268cf102b2f45809905bcb27ce8b, type: 3}
    m_Tooltip: 
    m_TextWithWhitespace: "HealthBarShader\u200B"
  m_Pos:
    serializedVersion: 2
    x: 327
    y: 79
    width: 641
    height: 607
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Selected: 039288a675cce344180833cccabcb512
  m_GraphObject: {fileID: 0}
  m_LastSerializedFileContents: "{\n    \"m_SGVersion\": 3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GraphData\",\n   
    \"m_ObjectId\": \"f04f1ea5a7794b8b92bf73b43fe8710d\",\n    \"m_Properties\":
    [\n        {\n            \"m_Id\": \"c9c08a61da3f4b91846c530d40bc6197\"\n       
    },\n        {\n            \"m_Id\": \"bdd5ae5247cf4355b990958ee68288ab\"\n       
    },\n        {\n            \"m_Id\": \"ebb44936a22c4135aac4ee737e27c36d\"\n       
    },\n        {\n            \"m_Id\": \"a6c0a805d23244b98a2d4b3d918189cd\"\n       
    }\n    ],\n    \"m_Keywords\": [],\n    \"m_Dropdowns\": [],\n    \"m_CategoryData\":
    [\n        {\n            \"m_Id\": \"2fd81457ab2d40f981c73231c769cac0\"\n       
    }\n    ],\n    \"m_Nodes\": [\n        {\n            \"m_Id\": \"0548453ff0914c28a744c5d13b8cfb2a\"\n       
    },\n        {\n            \"m_Id\": \"14d6571898304e069cf7b3f57b2b1c96\"\n       
    },\n        {\n            \"m_Id\": \"8b73cef92c9b4ef78cccc74302cdb833\"\n       
    },\n        {\n            \"m_Id\": \"ec132a262913445592c31d096441d347\"\n       
    },\n        {\n            \"m_Id\": \"ca73ac16adad416d8bf858aeb65ed5bf\"\n       
    },\n        {\n            \"m_Id\": \"90fdf1315767484c8ef4e8a75a6c83f0\"\n       
    },\n        {\n            \"m_Id\": \"9f5617349d6140a898031ce163f66ed8\"\n       
    },\n        {\n            \"m_Id\": \"82a06a0289d14b1a8c9e63ebf5683931\"\n       
    },\n        {\n            \"m_Id\": \"7e9744256cbc4fdea7a11b08d3f7473e\"\n       
    },\n        {\n            \"m_Id\": \"f547fe9ed5f94fb8a46c932c9289a702\"\n       
    },\n        {\n            \"m_Id\": \"f32d4e6e802948fba55b2ea7932b5425\"\n       
    },\n        {\n            \"m_Id\": \"645ac7750fb7416d85560189c5290591\"\n       
    },\n        {\n            \"m_Id\": \"0f7c7c073628408f9eab1fc1443b1550\"\n       
    },\n        {\n            \"m_Id\": \"412bda796ede41d891b13e551ca224e2\"\n       
    },\n        {\n            \"m_Id\": \"68610f31f0534563aca73a29fa959d0c\"\n       
    },\n        {\n            \"m_Id\": \"374ed06508af403684331902e2fc23f0\"\n       
    },\n        {\n            \"m_Id\": \"d03e8999406e479fa8f9311cef5835b5\"\n       
    },\n        {\n            \"m_Id\": \"b8cbe4ab977a44e09c2ff349b31d107e\"\n       
    },\n        {\n            \"m_Id\": \"a16d9185f0094ba1b153203a1da66e43\"\n       
    },\n        {\n            \"m_Id\": \"60cc9e580cf445f48b78108c78826e15\"\n       
    },\n        {\n            \"m_Id\": \"8c2a3708a85f46a49e943ab147912208\"\n       
    },\n        {\n            \"m_Id\": \"aa6195da55ca42618da61d137813b51b\"\n       
    },\n        {\n            \"m_Id\": \"d65f31a18ddf473d8a8fe401eaaa83c7\"\n       
    },\n        {\n            \"m_Id\": \"dfc9566c451e4cafb1e24cb580efe02e\"\n       
    },\n        {\n            \"m_Id\": \"2da01c58fffa4dc281d7ac40ef70b256\"\n       
    },\n        {\n            \"m_Id\": \"fb4b4427ceef4124ad02b181c019d735\"\n       
    }\n    ],\n    \"m_GroupDatas\": [\n        {\n            \"m_Id\": \"d2c9d1ea13d44276a5847e4001f23c9e\"\n       
    }\n    ],\n    \"m_StickyNoteDatas\": [],\n    \"m_Edges\": [\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"0f7c7c073628408f9eab1fc1443b1550\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"645ac7750fb7416d85560189c5290591\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"14d6571898304e069cf7b3f57b2b1c96\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"8b73cef92c9b4ef78cccc74302cdb833\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"14d6571898304e069cf7b3f57b2b1c96\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"d03e8999406e479fa8f9311cef5835b5\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"2da01c58fffa4dc281d7ac40ef70b256\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"0548453ff0914c28a744c5d13b8cfb2a\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"2da01c58fffa4dc281d7ac40ef70b256\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"fb4b4427ceef4124ad02b181c019d735\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"374ed06508af403684331902e2fc23f0\"\n                },\n                \"m_SlotId\":
    4\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"14d6571898304e069cf7b3f57b2b1c96\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"374ed06508af403684331902e2fc23f0\"\n                },\n                \"m_SlotId\":
    4\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"9f5617349d6140a898031ce163f66ed8\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"412bda796ede41d891b13e551ca224e2\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"0f7c7c073628408f9eab1fc1443b1550\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"60cc9e580cf445f48b78108c78826e15\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"a16d9185f0094ba1b153203a1da66e43\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"645ac7750fb7416d85560189c5290591\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"d03e8999406e479fa8f9311cef5835b5\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"7e9744256cbc4fdea7a11b08d3f7473e\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"a16d9185f0094ba1b153203a1da66e43\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"7e9744256cbc4fdea7a11b08d3f7473e\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"f32d4e6e802948fba55b2ea7932b5425\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"82a06a0289d14b1a8c9e63ebf5683931\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"412bda796ede41d891b13e551ca224e2\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"8b73cef92c9b4ef78cccc74302cdb833\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"ec132a262913445592c31d096441d347\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"90fdf1315767484c8ef4e8a75a6c83f0\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"a16d9185f0094ba1b153203a1da66e43\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"9f5617349d6140a898031ce163f66ed8\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"ec132a262913445592c31d096441d347\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"a16d9185f0094ba1b153203a1da66e43\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"9f5617349d6140a898031ce163f66ed8\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"b8cbe4ab977a44e09c2ff349b31d107e\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"2da01c58fffa4dc281d7ac40ef70b256\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"ca73ac16adad416d8bf858aeb65ed5bf\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"8b73cef92c9b4ef78cccc74302cdb833\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"d03e8999406e479fa8f9311cef5835b5\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"b8cbe4ab977a44e09c2ff349b31d107e\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"ec132a262913445592c31d096441d347\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"2da01c58fffa4dc281d7ac40ef70b256\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"f32d4e6e802948fba55b2ea7932b5425\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"645ac7750fb7416d85560189c5290591\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"f547fe9ed5f94fb8a46c932c9289a702\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"7e9744256cbc4fdea7a11b08d3f7473e\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"fb4b4427ceef4124ad02b181c019d735\"\n                },\n                \"m_SlotId\":
    4\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"68610f31f0534563aca73a29fa959d0c\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        }\n    ],\n    \"m_VertexContext\":
    {\n        \"m_Position\": {\n            \"x\": 1353.9998779296875,\n           
    \"y\": 0.9999964237213135\n        },\n        \"m_Blocks\": [\n            {\n               
    \"m_Id\": \"8c2a3708a85f46a49e943ab147912208\"\n            },\n            {\n               
    \"m_Id\": \"aa6195da55ca42618da61d137813b51b\"\n            },\n            {\n               
    \"m_Id\": \"d65f31a18ddf473d8a8fe401eaaa83c7\"\n            }\n        ]\n   
    },\n    \"m_FragmentContext\": {\n        \"m_Position\": {\n            \"x\":
    1353.9998779296875,\n            \"y\": 200.9999542236328\n        },\n       
    \"m_Blocks\": [\n            {\n                \"m_Id\": \"0548453ff0914c28a744c5d13b8cfb2a\"\n           
    },\n            {\n                \"m_Id\": \"68610f31f0534563aca73a29fa959d0c\"\n           
    },\n            {\n                \"m_Id\": \"dfc9566c451e4cafb1e24cb580efe02e\"\n           
    }\n        ]\n    },\n    \"m_PreviewData\": {\n        \"serializedMesh\": {\n           
    \"m_SerializedMesh\": \"{\\\"mesh\\\":{\\\"fileID\\\":-7963287049206491953,\\\"guid\\\":\\\"bd323d8a0ba02ba4ca8afe432a7e76d8\\\",\\\"type\\\":3}}\",\n           
    \"m_Guid\": \"\"\n        },\n        \"preventRotation\": false\n    },\n   
    \"m_Path\": \"Shader Graphs\",\n    \"m_GraphPrecision\": 2,\n    \"m_PreviewMode\":
    2,\n    \"m_OutputNode\": {\n        \"m_Id\": \"\"\n    },\n    \"m_SubDatas\":
    [],\n    \"m_ActiveTargets\": [\n        {\n            \"m_Id\": \"97193b4de84d49c0a4a092f7a1174f13\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"034ddf4e0f714b0bad97989876d3df00\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"InternalColor\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"03ee6bd3a2b54061bb0335e21caadda9\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"0548453ff0914c28a744c5d13b8cfb2a\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.BaseColor\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"fcb4bc09c2644418848da3749b064aaa\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 2,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.BaseColor\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"06f31d7f026f46d1920d432f7856d4f6\",\n   
    \"m_Id\": 3,\n    \"m_DisplayName\": \"Radius\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Radius\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": 0.05999999865889549,\n    \"m_DefaultValue\": 0.10000000149011612,\n   
    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.OneMinusNode\",\n   
    \"m_ObjectId\": \"0f7c7c073628408f9eab1fc1443b1550\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"One Minus\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -685.0000610351563,\n            \"y\": 1535.0001220703125,\n           
    \"width\": 208.0,\n            \"height\": 278.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"43fe3c699e96404797bd5f7728e99495\"\n       
    },\n        {\n            \"m_Id\": \"1e76f1aa85ed4916ada284c37f86dba3\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"complement\",\n        \"invert\",\n       
    \"opposite\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.OneMinusNode\",\n    \"m_ObjectId\":
    \"14d6571898304e069cf7b3f57b2b1c96\",\n    \"m_Group\": {\n        \"m_Id\":
    \"d2c9d1ea13d44276a5847e4001f23c9e\"\n    },\n    \"m_Name\": \"One Minus\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -193.00003051757813,\n           
    \"y\": -16.999990463256837,\n            \"width\": 208.0,\n            \"height\":
    278.0000305175781\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"ddb07b52a1d84b7cbfe2341cb7d6edcc\"\n        },\n        {\n           
    \"m_Id\": \"61c93cd367ac4af8929c38ff409fff96\"\n        }\n    ],\n    \"synonyms\":
    [\n        \"complement\",\n        \"invert\",\n        \"opposite\"\n    ],\n   
    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    }\n}\n\n{\n    \"m_SGVersion\": 2,\n    \"m_Type\": \"UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget\",\n   
    \"m_ObjectId\": \"15aeb03abd2f430c97086b68c706c70c\"\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"15d0dd1d6732417b81e16c7be505359c\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\":
    \"16b88650279e41d88e4e4545350a248a\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"e00\":
    0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n       
    \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\": 0.0,\n        \"e13\":
    0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 0.0,\n       
    \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\":
    0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"e00\":
    1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n       
    \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\": 0.0,\n        \"e13\":
    0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 1.0,\n       
    \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\":
    0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.NormalMaterialSlot\",\n    \"m_ObjectId\": \"19caa164276743ea888304e917b77bc0\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Normal\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Normal\",\n    \"m_StageCapability\":
    1,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"1c6c7bec6ba54fe9a6acc025f4ac3965\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\": 2.0,\n       
    \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n        \"e12\":
    2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\": 2.0,\n       
    \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n        \"e31\":
    2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"1e76f1aa85ed4916ada284c37f86dba3\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\":
    \"24898ebac40749bbb61d16093d63af6d\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"e00\": 2.0,\n       
    \"e01\": 2.0,\n        \"e02\": 2.0,\n        \"e03\": 2.0,\n        \"e10\":
    2.0,\n        \"e11\": 2.0,\n        \"e12\": 2.0,\n        \"e13\": 2.0,\n       
    \"e20\": 2.0,\n        \"e21\": 2.0,\n        \"e22\": 2.0,\n        \"e23\":
    2.0,\n        \"e30\": 2.0,\n        \"e31\": 2.0,\n        \"e32\": 2.0,\n       
    \"e33\": 2.0\n    },\n    \"m_DefaultValue\": {\n        \"e00\": 1.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 1.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 1.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"24d51373967945dd9eb9a078557b693f\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"R\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"24fad9c00d594926a989eade14b3705e\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"27347da8a194441f95bf2de8dbeeef4f\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"Min\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Min\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\":
    \"2da01c58fffa4dc281d7ac40ef70b256\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 867.9999389648438,\n            \"y\": 156.99998474121095,\n           
    \"width\": 208.00006103515626,\n            \"height\": 302.0\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"de7e385f1f924a29924977ba7defb635\"\n       
    },\n        {\n            \"m_Id\": \"24898ebac40749bbb61d16093d63af6d\"\n       
    },\n        {\n            \"m_Id\": \"16b88650279e41d88e4e4545350a248a\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.CategoryData\",\n    \"m_ObjectId\":
    \"2fd81457ab2d40f981c73231c769cac0\",\n    \"m_Name\": \"\",\n    \"m_ChildObjectList\":
    [\n        {\n            \"m_Id\": \"c9c08a61da3f4b91846c530d40bc6197\"\n       
    },\n        {\n            \"m_Id\": \"bdd5ae5247cf4355b990958ee68288ab\"\n       
    },\n        {\n            \"m_Id\": \"ebb44936a22c4135aac4ee737e27c36d\"\n       
    },\n        {\n            \"m_Id\": \"a6c0a805d23244b98a2d4b3d918189cd\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"30f074bf07a64aa68426d24cb0380aed\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"36b0a7780068463696006c08cf81122a\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"T\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"T\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.RoundedRectangleNode\",\n   
    \"m_ObjectId\": \"374ed06508af403684331902e2fc23f0\",\n    \"m_Group\": {\n       
    \"m_Id\": \"d2c9d1ea13d44276a5847e4001f23c9e\"\n    },\n    \"m_Name\": \"Rounded
    Rectangle\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -427.0,\n            \"y\": -26.999996185302736,\n            \"width\": 207.99998474121095,\n           
    \"height\": 350.0\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"3944317ae672460489556beeb88c0bf8\"\n        },\n        {\n           
    \"m_Id\": \"ca7e3eb5b1894ab6810d0199084e0b7f\"\n        },\n        {\n           
    \"m_Id\": \"6bb8ebfd68114309a8ac03d475ddc8c9\"\n        },\n        {\n           
    \"m_Id\": \"06f31d7f026f46d1920d432f7856d4f6\"\n        },\n        {\n           
    \"m_Id\": \"88d5ca760efa412e847621cfea1924d0\"\n        }\n    ],\n    \"synonyms\":
    [\n        \"square\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.UVMaterialSlot\",\n    \"m_ObjectId\":
    \"3944317ae672460489556beeb88c0bf8\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"UV\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"UV\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0\n    },\n    \"m_Labels\": [],\n    \"m_Channel\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"3af4d8c7b99d4668aa44c7811526d514\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SplitNode\",\n    \"m_ObjectId\":
    \"412bda796ede41d891b13e551ca224e2\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Split\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": -1017.9999389648438,\n            \"y\": 1346.0,\n            \"width\":
    119.99993896484375,\n            \"height\": 149.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"15d0dd1d6732417b81e16c7be505359c\"\n       
    },\n        {\n            \"m_Id\": \"24d51373967945dd9eb9a078557b693f\"\n       
    },\n        {\n            \"m_Id\": \"718bba4c109a4843a7f5d1b2583b99c6\"\n       
    },\n        {\n            \"m_Id\": \"24fad9c00d594926a989eade14b3705e\"\n       
    },\n        {\n            \"m_Id\": \"a924672c5d984c978861ce8d3df1df36\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"separate\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"421f5358825c438fbd72c892a1f3a8bc\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Edge\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Edge\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"43fe3c699e96404797bd5f7728e99495\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 1.0,\n       
    \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\": 1.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"504e8e746af64b1a9051a3fa7f94e5bf\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"51e86d19c6bc446eb60c57a6d99cf7f1\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"53208af92d7a49f8bb64bdb7e5982265\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Max\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Max\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"56b7c9d333c34285becb7abca15c0015\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"56e4431c123342f2a2e4fd9b346f0b83\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"G\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"5dd5ebdc674a46eca958beb6d3dd3b43\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"60c552efab2349ad99c6277eb4e47753\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Alpha Clip Threshold\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"AlphaClipThreshold\",\n    \"m_StageCapability\": 2,\n   
    \"m_Value\": 0.5,\n    \"m_DefaultValue\": 0.5,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"60cc9e580cf445f48b78108c78826e15\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -470.0,\n            \"y\": 702.0,\n            \"width\":
    151.0,\n            \"height\": 34.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"a50086858c3240c985d12bc15dabd8b7\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"ebb44936a22c4135aac4ee737e27c36d\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"61c93cd367ac4af8929c38ff409fff96\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.StepNode\",\n    \"m_ObjectId\": \"645ac7750fb7416d85560189c5290591\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Step\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -252.0000457763672,\n           
    \"y\": 1217.0,\n            \"width\": 208.0,\n            \"height\": 302.0\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"421f5358825c438fbd72c892a1f3a8bc\"\n       
    },\n        {\n            \"m_Id\": \"dc74dc7d862d4d94a98d37dc3d625ec3\"\n       
    },\n        {\n            \"m_Id\": \"73ad7caa2a284948b7cfd30c9fe0e92c\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"65c21d1e69f141b686bdb4db76982efe\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"R\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"68610f31f0534563aca73a29fa959d0c\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Alpha\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"a6a70d0c674f4e8d965167dad2375038\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 2,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Alpha\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"6bb8ebfd68114309a8ac03d475ddc8c9\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Height\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Height\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": 0.8299999833106995,\n    \"m_DefaultValue\": 0.5,\n    \"m_Labels\":
    []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"6e4806f615f047d88b3f09b67e866323\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"718bba4c109a4843a7f5d1b2583b99c6\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"G\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"73ad7caa2a284948b7cfd30c9fe0e92c\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"751194c37dc44147a3f73bb59209ff7a\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\":
    \"7774e94e2213499ca8b92730346b1b28\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"7d32cf82559846179fa4ba4ce5834cda\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\":
    \"7e9744256cbc4fdea7a11b08d3f7473e\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1065.0,\n            \"y\": 701.9999389648438,\n           
    \"width\": 208.00006103515626,\n            \"height\": 302.00006103515627\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"3af4d8c7b99d4668aa44c7811526d514\"\n       
    },\n        {\n            \"m_Id\": \"9010eddd42ce492b930b61ce3ea09be5\"\n       
    },\n        {\n            \"m_Id\": \"d0fb441a3f9146aca1a02c38f0d438e0\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"7ec9e6e03d204092b02a93babd2b0197\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.UVNode\",\n    \"m_ObjectId\": \"82a06a0289d14b1a8c9e63ebf5683931\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"UV\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1358.9998779296875,\n           
    \"y\": 1181.9998779296875,\n            \"width\": 208.0,\n            \"height\":
    313.0\n        }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\":
    \"7774e94e2213499ca8b92730346b1b28\"\n        }\n    ],\n    \"synonyms\": [\n       
    \"texcoords\",\n        \"coords\",\n        \"coordinates\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    },\n   
    \"m_OutputChannel\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"82a9bd3bc2d14a18904d2ebde6f30c4e\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"88d5ca760efa412e847621cfea1924d0\",\n    \"m_Id\": 4,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\": \"8b73cef92c9b4ef78cccc74302cdb833\",\n   
    \"m_Group\": {\n        \"m_Id\": \"d2c9d1ea13d44276a5847e4001f23c9e\"\n    },\n   
    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    152.0,\n            \"y\": -44.9999885559082,\n            \"width\": 208.00003051757813,\n           
    \"height\": 302.0000305175781\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"5dd5ebdc674a46eca958beb6d3dd3b43\"\n        },\n       
    {\n            \"m_Id\": \"d4113f0658804be98e6f70052dbd0ff6\"\n        },\n       
    {\n            \"m_Id\": \"d19606fd81af4603b6c91d89d0550eb9\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"8c2a3708a85f46a49e943ab147912208\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"VertexDescription.Position\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"a0ca1f1698ad4a0498586a18b85813c4\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Position\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"8e742f6ef699452093311b24fa11ae05\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\":
    \"8f8b8c574f31410297f9730ebb1317fa\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"BaseColor\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"9010eddd42ce492b930b61ce3ea09be5\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 1.0,\n        \"e01\": 2.0,\n        \"e02\":
    2.0,\n        \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n       
    \"e12\": 2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\":
    2.0,\n        \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n       
    \"e31\": 2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"90fdf1315767484c8ef4e8a75a6c83f0\",\n    \"m_Group\": {\n        \"m_Id\":
    \"d2c9d1ea13d44276a5847e4001f23c9e\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -415.0000305175781,\n           
    \"y\": 346.0000305175781,\n            \"width\": 144.0,\n            \"height\":
    33.999969482421878\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"034ddf4e0f714b0bad97989876d3df00\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"bdd5ae5247cf4355b990958ee68288ab\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 1,\n    \"m_Type\": \"UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget\",\n   
    \"m_ObjectId\": \"97193b4de84d49c0a4a092f7a1174f13\",\n    \"m_Datas\": [],\n   
    \"m_ActiveSubTarget\": {\n        \"m_Id\": \"15aeb03abd2f430c97086b68c706c70c\"\n   
    },\n    \"m_AllowMaterialOverride\": true,\n    \"m_SurfaceType\": 1,\n    \"m_ZTestMode\":
    4,\n    \"m_ZWriteControl\": 0,\n    \"m_AlphaMode\": 0,\n    \"m_RenderFace\":
    2,\n    \"m_AlphaClip\": true,\n    \"m_CastShadows\": false,\n    \"m_ReceiveShadows\":
    false,\n    \"m_DisableTint\": false,\n    \"m_AdditionalMotionVectorMode\":
    0,\n    \"m_AlembicMotionVectors\": false,\n    \"m_SupportsLODCrossFade\": false,\n   
    \"m_CustomEditorGUI\": \"\",\n    \"m_SupportVFX\": false\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"98a6170f04224ffc8ae4ddc7c9fa749e\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.TangentMaterialSlot\",\n    \"m_ObjectId\":
    \"9d0c3ac1bd18478caab2637f02954613\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Tangent\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Tangent\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\": \"9f5617349d6140a898031ce163f66ed8\",\n   
    \"m_Group\": {\n        \"m_Id\": \"d2c9d1ea13d44276a5847e4001f23c9e\"\n    },\n   
    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    171.0,\n            \"y\": 322.9999694824219,\n            \"width\": 207.99996948242188,\n           
    \"height\": 301.9999694824219\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"e1c1668bb4384133bf877a10de68dae7\"\n        },\n       
    {\n            \"m_Id\": \"1c6c7bec6ba54fe9a6acc025f4ac3965\"\n        },\n       
    {\n            \"m_Id\": \"beb9c98d325149439ebba9ce56c0836e\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PositionMaterialSlot\",\n    \"m_ObjectId\":
    \"a0ca1f1698ad4a0498586a18b85813c4\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Position\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Position\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.LerpNode\",\n    \"m_ObjectId\": \"a16d9185f0094ba1b153203a1da66e43\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Lerp\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -192.99996948242188,\n           
    \"y\": 665.0,\n            \"width\": 208.00001525878907,\n            \"height\":
    325.9998779296875\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"7ec9e6e03d204092b02a93babd2b0197\"\n        },\n        {\n           
    \"m_Id\": \"03ee6bd3a2b54061bb0335e21caadda9\"\n        },\n        {\n           
    \"m_Id\": \"36b0a7780068463696006c08cf81122a\"\n        },\n        {\n           
    \"m_Id\": \"98a6170f04224ffc8ae4ddc7c9fa749e\"\n        }\n    ],\n    \"synonyms\":
    [\n        \"mix\",\n        \"blend\",\n        \"linear interpolate\"\n   
    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"a50086858c3240c985d12bc15dabd8b7\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"InternalColor2\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"a6a70d0c674f4e8d965167dad2375038\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Alpha\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Alpha\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    1.0,\n    \"m_DefaultValue\": 1.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    1,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty\",\n   
    \"m_ObjectId\": \"a6c0a805d23244b98a2d4b3d918189cd\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"e5fb540a-45fb-4e50-81d2-bd5343088e61\"\n    },\n    \"m_Name\":
    \"Health\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"Health\",\n    \"m_DefaultReferenceName\": \"_Health\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": 1.0,\n    \"m_FloatType\": 1,\n   
    \"m_RangeValues\": {\n        \"x\": 0.0,\n        \"y\": 1.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"a924672c5d984c978861ce8d3df1df36\",\n    \"m_Id\": 4,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"a99e31ecba5248518759ae2f4e2e473a\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"aa6195da55ca42618da61d137813b51b\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"VertexDescription.Normal\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"19caa164276743ea888304e917b77bc0\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Normal\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"b333584b3613472da569f5ae87f83060\",\n   
    \"m_Id\": 3,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ClampNode\",\n    \"m_ObjectId\":
    \"b8cbe4ab977a44e09c2ff349b31d107e\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Clamp\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": 476.00006103515627,\n            \"y\": 1209.0,\n            \"width\":
    208.0,\n            \"height\": 326.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"7d32cf82559846179fa4ba4ce5834cda\"\n        },\n       
    {\n            \"m_Id\": \"27347da8a194441f95bf2de8dbeeef4f\"\n        },\n       
    {\n            \"m_Id\": \"53208af92d7a49f8bb64bdb7e5982265\"\n        },\n       
    {\n            \"m_Id\": \"b333584b3613472da569f5ae87f83060\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"limit\"\n    ],\n    \"m_Precision\": 0,\n   
    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.ColorShaderProperty\",\n   
    \"m_ObjectId\": \"bdd5ae5247cf4355b990958ee68288ab\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"79ea6372-9686-4e16-aa65-23f15b314031\"\n    },\n    \"m_Name\":
    \"InternalColor\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"InternalColor\",\n    \"m_DefaultReferenceName\": \"_InternalColor\",\n   
    \"m_OverrideReferenceName\": \"_Color1\",\n    \"m_GeneratePropertyBlock\": true,\n   
    \"m_UseCustomSlotLabel\": false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\":
    0,\n    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"r\": 0.23358654975891114,\n       
    \"g\": 1.0,\n        \"b\": 0.0,\n        \"a\": 1.0\n    },\n    \"isMainColor\":
    false,\n    \"m_ColorMode\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"beb9c98d325149439ebba9ce56c0836e\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\":
    0.0,\n        \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n       
    \"e12\": 0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\":
    0.0,\n        \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n       
    \"e31\": 0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.ColorShaderProperty\",\n   
    \"m_ObjectId\": \"c9c08a61da3f4b91846c530d40bc6197\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"9eeef1fe-db71-4a85-8dc1-8dbc5bf57be0\"\n    },\n    \"m_Name\":
    \"BaseColor\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"BaseColor\",\n    \"m_DefaultReferenceName\": \"_BaseColor\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"r\": 0.0,\n       
    \"g\": 0.0,\n        \"b\": 0.0,\n        \"a\": 1.0\n    },\n    \"isMainColor\":
    false,\n    \"m_ColorMode\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"ca73ac16adad416d8bf858aeb65ed5bf\",\n   
    \"m_Group\": {\n        \"m_Id\": \"d2c9d1ea13d44276a5847e4001f23c9e\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -115.99996185302735,\n            \"y\": -79.0,\n            \"width\": 130.99996948242188,\n           
    \"height\": 34.0\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"8f8b8c574f31410297f9730ebb1317fa\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"c9c08a61da3f4b91846c530d40bc6197\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"ca7e3eb5b1894ab6810d0199084e0b7f\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Width\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Width\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.949999988079071,\n    \"m_DefaultValue\": 0.5,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"caf1f2161ee048d38b97284763261818\",\n    \"m_Id\": 4,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n    \"m_ObjectId\":
    \"d03e8999406e479fa8f9311cef5835b5\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Add\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": 82.0000228881836,\n            \"y\": 1217.0,\n            \"width\":
    208.0,\n            \"height\": 302.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"51e86d19c6bc446eb60c57a6d99cf7f1\"\n        },\n       
    {\n            \"m_Id\": \"8e742f6ef699452093311b24fa11ae05\"\n        },\n       
    {\n            \"m_Id\": \"6e4806f615f047d88b3f09b67e866323\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n        \"plus\"\n   
    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"d0fb441a3f9146aca1a02c38f0d438e0\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"d19606fd81af4603b6c91d89d0550eb9\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\":
    \"d2c9d1ea13d44276a5847e4001f23c9e\",\n    \"m_Title\": \"Color\",\n    \"m_Position\":
    {\n        \"x\": -452.0,\n        \"y\": -138.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"d4113f0658804be98e6f70052dbd0ff6\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\": 2.0,\n       
    \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n        \"e12\":
    2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\": 2.0,\n       
    \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n        \"e31\":
    2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"d65f31a18ddf473d8a8fe401eaaa83c7\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"VertexDescription.Tangent\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"9d0c3ac1bd18478caab2637f02954613\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Tangent\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"dc74dc7d862d4d94a98d37dc3d625ec3\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"ddb07b52a1d84b7cbfe2341cb7d6edcc\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\":
    \"de7e385f1f924a29924977ba7defb635\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"e00\": 0.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 0.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 0.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"e00\": 1.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 1.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 1.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"dfc9566c451e4cafb1e24cb580efe02e\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.AlphaClipThreshold\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"60c552efab2349ad99c6277eb4e47753\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.AlphaClipThreshold\"\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\":
    \"e1c1668bb4384133bf877a10de68dae7\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"e00\": 0.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 0.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 0.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"e00\": 1.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 1.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 1.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.ColorShaderProperty\",\n   
    \"m_ObjectId\": \"ebb44936a22c4135aac4ee737e27c36d\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"a8d4f50e-eb3f-4609-95d2-41ac2c3c8d12\"\n    },\n    \"m_Name\":
    \"InternalColor2\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"InternalColor2\",\n    \"m_DefaultReferenceName\": \"_InternalColor2\",\n   
    \"m_OverrideReferenceName\": \"_Color2\",\n    \"m_GeneratePropertyBlock\": true,\n   
    \"m_UseCustomSlotLabel\": false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\":
    0,\n    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"r\": 1.0,\n       
    \"g\": 0.019191347062587739,\n        \"b\": 0.0,\n        \"a\": 1.0\n    },\n   
    \"isMainColor\": false,\n    \"m_ColorMode\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n    \"m_ObjectId\":
    \"ec132a262913445592c31d096441d347\",\n    \"m_Group\": {\n        \"m_Id\":
    \"d2c9d1ea13d44276a5847e4001f23c9e\"\n    },\n    \"m_Name\": \"Add\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 528.0000610351563,\n            \"y\": 60.999996185302737,\n           
    \"width\": 208.0,\n            \"height\": 302.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"82a9bd3bc2d14a18904d2ebde6f30c4e\"\n       
    },\n        {\n            \"m_Id\": \"a99e31ecba5248518759ae2f4e2e473a\"\n       
    },\n        {\n            \"m_Id\": \"56b7c9d333c34285becb7abca15c0015\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n       
    \"plus\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"ec707c8696874b639a4446d41659e586\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"f07af359a3fc42238d5c00263cd888ef\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Health\",\n    \"m_SlotType\": 1,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.OneMinusNode\",\n   
    \"m_ObjectId\": \"f32d4e6e802948fba55b2ea7932b5425\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"One Minus\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -685.0,\n            \"y\": 1217.0,\n            \"width\":
    208.0,\n            \"height\": 278.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"751194c37dc44147a3f73bb59209ff7a\"\n        },\n       
    {\n            \"m_Id\": \"30f074bf07a64aa68426d24cb0380aed\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"complement\",\n        \"invert\",\n       
    \"opposite\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"f547fe9ed5f94fb8a46c932c9289a702\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1260.9998779296875,\n            \"y\": 701.9999389648438,\n           
    \"width\": 110.0,\n            \"height\": 34.00006103515625\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"f07af359a3fc42238d5c00263cd888ef\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"a6c0a805d23244b98a2d4b3d918189cd\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SplitNode\",\n    \"m_ObjectId\":
    \"fb4b4427ceef4124ad02b181c019d735\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Split\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": 1107.0001220703125,\n            \"y\": 426.0000305175781,\n           
    \"width\": 120.0,\n            \"height\": 149.00003051757813\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"504e8e746af64b1a9051a3fa7f94e5bf\"\n       
    },\n        {\n            \"m_Id\": \"65c21d1e69f141b686bdb4db76982efe\"\n       
    },\n        {\n            \"m_Id\": \"56e4431c123342f2a2e4fd9b346f0b83\"\n       
    },\n        {\n            \"m_Id\": \"ec707c8696874b639a4446d41659e586\"\n       
    },\n        {\n            \"m_Id\": \"caf1f2161ee048d38b97284763261818\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"separate\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ColorRGBMaterialSlot\",\n   
    \"m_ObjectId\": \"fcb4bc09c2644418848da3749b064aaa\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Base Color\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"BaseColor\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": {\n        \"x\": 0.5,\n        \"y\": 0.5,\n        \"z\":
    0.5\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.5,\n        \"y\": 0.5,\n       
    \"z\": 0.5\n    },\n    \"m_Labels\": [],\n    \"m_ColorMode\": 0,\n    \"m_DefaultColor\":
    {\n        \"r\": 0.5,\n        \"g\": 0.5,\n        \"b\": 0.5,\n        \"a\":
    1.0\n    }\n}\n\n"
  m_AssetMaybeChangedOnDisk: 0
  m_AssetMaybeDeleted: 0
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 924ffcbe75518854f97b48776d0f1939, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.ShaderGraph.Editor::UnityEditor.ShaderGraph.Drawing.MaterialGraphEditWindow
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: TileShader
    m_Image: {fileID: 2800000, guid: 7129268cf102b2f45809905bcb27ce8b, type: 3}
    m_Tooltip: 
    m_TextWithWhitespace: "TileShader\u200B"
  m_Pos:
    serializedVersion: 2
    x: 309
    y: 79
    width: 1158
    height: 638
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Selected: e53ee108dc625d247bd4b84bc0c09a90
  m_GraphObject: {fileID: 0}
  m_LastSerializedFileContents: "{\n    \"m_SGVersion\": 3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GraphData\",\n   
    \"m_ObjectId\": \"6f87995e90f6446d977124889b5b9c73\",\n    \"m_Properties\":
    [\n        {\n            \"m_Id\": \"9934602af1e24eceb2ef05e93ab199a3\"\n       
    },\n        {\n            \"m_Id\": \"b633a40b3dac40c1b125301b132ca4bf\"\n       
    },\n        {\n            \"m_Id\": \"f1d48354f1404fc8b1054cf3de4534a8\"\n       
    },\n        {\n            \"m_Id\": \"fb6c2c0d2b694b4b90c76789f3eb2818\"\n       
    },\n        {\n            \"m_Id\": \"af0425805a614ca19323990aff0904d4\"\n       
    }\n    ],\n    \"m_Keywords\": [],\n    \"m_Dropdowns\": [],\n    \"m_CategoryData\":
    [\n        {\n            \"m_Id\": \"e605249f5a0a46e6ac17aa01e0de40b5\"\n       
    },\n        {\n            \"m_Id\": \"0fea8266519b44f9a35759fe6e415aa2\"\n       
    }\n    ],\n    \"m_Nodes\": [\n        {\n            \"m_Id\": \"0f1483ecff0a4bd8bcbb64c4171518c3\"\n       
    },\n        {\n            \"m_Id\": \"55bea76e3d424776a968e2d1376eddfb\"\n       
    },\n        {\n            \"m_Id\": \"2a39df58aca143bca7e47d94d16cef44\"\n       
    },\n        {\n            \"m_Id\": \"66d9934af61f4ea5bcfa197e44e8187a\"\n       
    },\n        {\n            \"m_Id\": \"8e8ed997226344a5b24d0dd911ad7835\"\n       
    },\n        {\n            \"m_Id\": \"c317623f83aa4fae8a0cc78023526497\"\n       
    },\n        {\n            \"m_Id\": \"bea831570c964bdc8e8689adde11b573\"\n       
    },\n        {\n            \"m_Id\": \"aed27217980a4f229a6059edd1f526ea\"\n       
    },\n        {\n            \"m_Id\": \"b1f91ad7f45c4ce2bc7b6a522887814e\"\n       
    },\n        {\n            \"m_Id\": \"3f9a12f4d77f4c79815c060e93cd6763\"\n       
    },\n        {\n            \"m_Id\": \"5637204faa3141a7a4475a6dfcc30ffa\"\n       
    },\n        {\n            \"m_Id\": \"e67fa66af20940a29d98e1100a835369\"\n       
    },\n        {\n            \"m_Id\": \"ef8de3a8eb5741279ace1f7127d02e9b\"\n       
    },\n        {\n            \"m_Id\": \"bf28280b984e4455936fc77ade09cd98\"\n       
    },\n        {\n            \"m_Id\": \"7db8956a8ca14ed18efc79062b78d6bf\"\n       
    },\n        {\n            \"m_Id\": \"1e1c9da091f747e5b7c761f36fa71107\"\n       
    },\n        {\n            \"m_Id\": \"13344c919744452ba8e0d4c45e91f369\"\n       
    },\n        {\n            \"m_Id\": \"7c74a537cefb4b778973a216c526ffa4\"\n       
    },\n        {\n            \"m_Id\": \"3836ae6cdda2414ca42dcd89667aa9bb\"\n       
    },\n        {\n            \"m_Id\": \"c85eb7b7cc934be8b3982ebe2fe29d41\"\n       
    },\n        {\n            \"m_Id\": \"de37e72fab8645cf88e1b1df2ac6aa8b\"\n       
    },\n        {\n            \"m_Id\": \"400da3e7a5c541d7a611b587c4bf1fba\"\n       
    },\n        {\n            \"m_Id\": \"0607cd1fcc2d4e178943f33edc10b10e\"\n       
    },\n        {\n            \"m_Id\": \"a0e9ee09913344f79f6b8c34debe29ec\"\n       
    },\n        {\n            \"m_Id\": \"4d2603e022d94134a01ca2b20d5646e0\"\n       
    },\n        {\n            \"m_Id\": \"fd7b00e0ff074b69a4736770df2a618c\"\n       
    },\n        {\n            \"m_Id\": \"09712371374e4a9ab4b7eaaf9e378e8b\"\n       
    },\n        {\n            \"m_Id\": \"b76b9fadfecc478287c52d3e9e822e63\"\n       
    },\n        {\n            \"m_Id\": \"a74090d898a24880917449f7e9d02f40\"\n       
    },\n        {\n            \"m_Id\": \"a58e4571017b496583fe5f75468ffd53\"\n       
    },\n        {\n            \"m_Id\": \"40766a93ed4940e9a82cae4a0ca4ccc4\"\n       
    },\n        {\n            \"m_Id\": \"7ee467498251407c9b5a72eb56e6197c\"\n       
    },\n        {\n            \"m_Id\": \"8f13a789650041528a35fc7c04d448eb\"\n       
    },\n        {\n            \"m_Id\": \"7d45750db9644ead88c1b2075195ede7\"\n       
    },\n        {\n            \"m_Id\": \"7da15f277c9e42d197fde57875d195bb\"\n       
    },\n        {\n            \"m_Id\": \"163d50f08bf34069846e3a89e14e76b6\"\n       
    },\n        {\n            \"m_Id\": \"9d56429c130342b186f3652eda93339f\"\n       
    },\n        {\n            \"m_Id\": \"11044ef507dd495685517b78c8ee1eaa\"\n       
    },\n        {\n            \"m_Id\": \"a5971218945548d69f32ea004a932cc1\"\n       
    },\n        {\n            \"m_Id\": \"972d0289dbef4be9aca67cb7dbf57258\"\n       
    }\n    ],\n    \"m_GroupDatas\": [\n        {\n            \"m_Id\": \"1e86dfc1e6264ce486e269c1140393a9\"\n       
    },\n        {\n            \"m_Id\": \"fb495be9e9a54fb7b4aa14ac604610b2\"\n       
    },\n        {\n            \"m_Id\": \"67f39673beae40e4a48012d83b2fb7d3\"\n       
    },\n        {\n            \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n       
    }\n    ],\n    \"m_StickyNoteDatas\": [],\n    \"m_Edges\": [\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"0607cd1fcc2d4e178943f33edc10b10e\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3836ae6cdda2414ca42dcd89667aa9bb\"\n               
    },\n                \"m_SlotId\": 710538326\n            }\n        },\n       
    {\n            \"m_OutputSlot\": {\n                \"m_Node\": {\n                   
    \"m_Id\": \"09712371374e4a9ab4b7eaaf9e378e8b\"\n                },\n               
    \"m_SlotId\": 2\n            },\n            \"m_InputSlot\": {\n               
    \"m_Node\": {\n                    \"m_Id\": \"a74090d898a24880917449f7e9d02f40\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"11044ef507dd495685517b78c8ee1eaa\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"09712371374e4a9ab4b7eaaf9e378e8b\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"13344c919744452ba8e0d4c45e91f369\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"b76b9fadfecc478287c52d3e9e822e63\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"163d50f08bf34069846e3a89e14e76b6\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"972d0289dbef4be9aca67cb7dbf57258\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"1e1c9da091f747e5b7c761f36fa71107\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"bf28280b984e4455936fc77ade09cd98\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3836ae6cdda2414ca42dcd89667aa9bb\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"7db8956a8ca14ed18efc79062b78d6bf\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3836ae6cdda2414ca42dcd89667aa9bb\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"13344c919744452ba8e0d4c45e91f369\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3836ae6cdda2414ca42dcd89667aa9bb\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"c317623f83aa4fae8a0cc78023526497\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3836ae6cdda2414ca42dcd89667aa9bb\"\n                },\n                \"m_SlotId\":
    4\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"11044ef507dd495685517b78c8ee1eaa\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3836ae6cdda2414ca42dcd89667aa9bb\"\n                },\n                \"m_SlotId\":
    4\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"8e8ed997226344a5b24d0dd911ad7835\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3f9a12f4d77f4c79815c060e93cd6763\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"5637204faa3141a7a4475a6dfcc30ffa\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"400da3e7a5c541d7a611b587c4bf1fba\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3836ae6cdda2414ca42dcd89667aa9bb\"\n               
    },\n                \"m_SlotId\": 1917425063\n            }\n        },\n       
    {\n            \"m_OutputSlot\": {\n                \"m_Node\": {\n                   
    \"m_Id\": \"40766a93ed4940e9a82cae4a0ca4ccc4\"\n                },\n               
    \"m_SlotId\": 0\n            },\n            \"m_InputSlot\": {\n               
    \"m_Node\": {\n                    \"m_Id\": \"7ee467498251407c9b5a72eb56e6197c\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"4d2603e022d94134a01ca2b20d5646e0\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"09712371374e4a9ab4b7eaaf9e378e8b\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"5637204faa3141a7a4475a6dfcc30ffa\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"163d50f08bf34069846e3a89e14e76b6\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"7c74a537cefb4b778973a216c526ffa4\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"aed27217980a4f229a6059edd1f526ea\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"7d45750db9644ead88c1b2075195ede7\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"a5971218945548d69f32ea004a932cc1\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"7da15f277c9e42d197fde57875d195bb\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"a58e4571017b496583fe5f75468ffd53\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"7ee467498251407c9b5a72eb56e6197c\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"8f13a789650041528a35fc7c04d448eb\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"8f13a789650041528a35fc7c04d448eb\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"7d45750db9644ead88c1b2075195ede7\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"972d0289dbef4be9aca67cb7dbf57258\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"7da15f277c9e42d197fde57875d195bb\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"9d56429c130342b186f3652eda93339f\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"163d50f08bf34069846e3a89e14e76b6\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"a0e9ee09913344f79f6b8c34debe29ec\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"4d2603e022d94134a01ca2b20d5646e0\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"a58e4571017b496583fe5f75468ffd53\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"66d9934af61f4ea5bcfa197e44e8187a\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"a5971218945548d69f32ea004a932cc1\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"7da15f277c9e42d197fde57875d195bb\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"a74090d898a24880917449f7e9d02f40\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"b76b9fadfecc478287c52d3e9e822e63\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"aed27217980a4f229a6059edd1f526ea\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3f9a12f4d77f4c79815c060e93cd6763\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"b1f91ad7f45c4ce2bc7b6a522887814e\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3f9a12f4d77f4c79815c060e93cd6763\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"b76b9fadfecc478287c52d3e9e822e63\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"a58e4571017b496583fe5f75468ffd53\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"bea831570c964bdc8e8689adde11b573\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"aed27217980a4f229a6059edd1f526ea\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"bf28280b984e4455936fc77ade09cd98\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"13344c919744452ba8e0d4c45e91f369\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"c85eb7b7cc934be8b3982ebe2fe29d41\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3836ae6cdda2414ca42dcd89667aa9bb\"\n               
    },\n                \"m_SlotId\": 916035060\n            }\n        },\n       
    {\n            \"m_OutputSlot\": {\n                \"m_Node\": {\n                   
    \"m_Id\": \"de37e72fab8645cf88e1b1df2ac6aa8b\"\n                },\n               
    \"m_SlotId\": 0\n            },\n            \"m_InputSlot\": {\n               
    \"m_Node\": {\n                    \"m_Id\": \"3836ae6cdda2414ca42dcd89667aa9bb\"\n               
    },\n                \"m_SlotId\": -1441464907\n            }\n        },\n       
    {\n            \"m_OutputSlot\": {\n                \"m_Node\": {\n                   
    \"m_Id\": \"ef8de3a8eb5741279ace1f7127d02e9b\"\n                },\n               
    \"m_SlotId\": 0\n            },\n            \"m_InputSlot\": {\n               
    \"m_Node\": {\n                    \"m_Id\": \"bf28280b984e4455936fc77ade09cd98\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"fd7b00e0ff074b69a4736770df2a618c\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"4d2603e022d94134a01ca2b20d5646e0\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        }\n    ],\n    \"m_VertexContext\":
    {\n        \"m_Position\": {\n            \"x\": 3218.66650390625,\n           
    \"y\": 139.33331298828126\n        },\n        \"m_Blocks\": [\n            {\n               
    \"m_Id\": \"0f1483ecff0a4bd8bcbb64c4171518c3\"\n            },\n            {\n               
    \"m_Id\": \"55bea76e3d424776a968e2d1376eddfb\"\n            },\n            {\n               
    \"m_Id\": \"2a39df58aca143bca7e47d94d16cef44\"\n            }\n        ]\n   
    },\n    \"m_FragmentContext\": {\n        \"m_Position\": {\n            \"x\":
    3218.66650390625,\n            \"y\": 391.33319091796877\n        },\n       
    \"m_Blocks\": [\n            {\n                \"m_Id\": \"66d9934af61f4ea5bcfa197e44e8187a\"\n           
    },\n            {\n                \"m_Id\": \"8e8ed997226344a5b24d0dd911ad7835\"\n           
    },\n            {\n                \"m_Id\": \"c317623f83aa4fae8a0cc78023526497\"\n           
    },\n            {\n                \"m_Id\": \"e67fa66af20940a29d98e1100a835369\"\n           
    },\n            {\n                \"m_Id\": \"7db8956a8ca14ed18efc79062b78d6bf\"\n           
    }\n        ]\n    },\n    \"m_PreviewData\": {\n        \"serializedMesh\": {\n           
    \"m_SerializedMesh\": \"{\\\"mesh\\\":{\\\"instanceID\\\":0}}\",\n           
    \"m_Guid\": \"\"\n        },\n        \"preventRotation\": false\n    },\n   
    \"m_Path\": \"Shader Graphs\",\n    \"m_GraphPrecision\": 1,\n    \"m_PreviewMode\":
    2,\n    \"m_OutputNode\": {\n        \"m_Id\": \"\"\n    },\n    \"m_SubDatas\":
    [],\n    \"m_ActiveTargets\": [\n        {\n            \"m_Id\": \"354209da57124c9c86f6dde3e094ed2d\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.UVNode\",\n   
    \"m_ObjectId\": \"0607cd1fcc2d4e178943f33edc10b10e\",\n    \"m_Group\": {\n       
    \"m_Id\": \"fb495be9e9a54fb7b4aa14ac604610b2\"\n    },\n    \"m_Name\": \"UV\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -808.0,\n            \"y\":
    522.6666259765625,\n            \"width\": 147.33343505859376,\n            \"height\":
    131.99993896484376\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"ecaa51a43ef644f1bb29ab7f0505c8ca\"\n        }\n    ],\n    \"synonyms\":
    [\n        \"texcoords\",\n        \"coords\",\n        \"coordinates\"\n   
    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_OutputChannel\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"0844f1ee1e754f8cbf3f4eefc21cc640\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 1.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n   
    \"m_ObjectId\": \"09712371374e4a9ab4b7eaaf9e378e8b\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 409.99993896484377,\n            \"y\": 1489.9998779296875,\n           
    \"width\": 209.33343505859376,\n            \"height\": 304.0\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"6e3c3349e414456099bb05eb4adeda04\"\n       
    },\n        {\n            \"m_Id\": \"6556c74e791348e2a1b09112090f6dd9\"\n       
    },\n        {\n            \"m_Id\": \"924e141cc7bf4dd6a378c10407f0641b\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DInputMaterialSlot\",\n   
    \"m_ObjectId\": \"0ab9ae7a6a6d4841b319b52ded49d746\",\n    \"m_Id\": 1917425063,\n   
    \"m_DisplayName\": \"MaskTex\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"_MaskTex\",\n    \"m_StageCapability\": 2,\n    \"m_BareResource\":
    false,\n    \"m_Texture\": {\n        \"m_SerializedTexture\": \"\",\n       
    \"m_Guid\": \"\"\n    },\n    \"m_DefaultType\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"0bd1ca0cc55a4fb58b71f3df3949575d\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.SamplerStateMaterialSlot\",\n    \"m_ObjectId\":
    \"0cf3aee95a9a478f8e0b6b26386b051c\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"Sampler\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Sampler\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\": false\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"0d0030026bbc4d5cb6f0874b216e7943\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Albedo\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Albedo\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\":
    \"0d778a62c57945a3ba410f2e577d9081\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"0dcdf811d31d462cbd9b73cc8f5d400c\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\":
    0.0,\n        \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n       
    \"e12\": 0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\":
    0.0,\n        \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n       
    \"e31\": 0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"0f1483ecff0a4bd8bcbb64c4171518c3\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"VertexDescription.Position\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"55ae4d2eaee0478ab9ea6da84de39934\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Position\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"0f2c1f24ce374126b96e69264c451e08\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.CategoryData\",\n    \"m_ObjectId\":
    \"0fea8266519b44f9a35759fe6e415aa2\",\n    \"m_Name\": \"Light\",\n    \"m_ChildObjectList\":
    [\n        {\n            \"m_Id\": \"af0425805a614ca19323990aff0904d4\"\n       
    },\n        {\n            \"m_Id\": \"f1d48354f1404fc8b1054cf3de4534a8\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SplitNode\",\n   
    \"m_ObjectId\": \"11044ef507dd495685517b78c8ee1eaa\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Split\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 233.9999542236328,\n            \"y\": 1507.333251953125,\n           
    \"width\": 120.66673278808594,\n            \"height\": 150.666748046875\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"8ca8dacc5fd347798114604538a28472\"\n       
    },\n        {\n            \"m_Id\": \"42713f51278b4eb99626c00cea7c1739\"\n       
    },\n        {\n            \"m_Id\": \"882338fe623443958bcfbccfd33a74f5\"\n       
    },\n        {\n            \"m_Id\": \"47872200e7d740ef9c9aad08a5a9850e\"\n       
    },\n        {\n            \"m_Id\": \"dd113f9f7f464680aea4f52e0a6e697c\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"separate\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n   
    \"m_ObjectId\": \"11c37652171245909af803f18ede41b4\",\n    \"m_Title\": \"Light
    & SPecular\",\n    \"m_Position\": {\n        \"x\": 409.0,\n        \"y\": -498.0\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n   
    \"m_ObjectId\": \"13344c919744452ba8e0d4c45e91f369\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 659.3333129882813,\n            \"y\": 1132.6666259765625,\n           
    \"width\": 209.3333740234375,\n            \"height\": 304.0001220703125\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"0dcdf811d31d462cbd9b73cc8f5d400c\"\n       
    },\n        {\n            \"m_Id\": \"e0b8095e1b334ef8b8882d06b41093a9\"\n       
    },\n        {\n            \"m_Id\": \"6aed059734ae4d34a59e08e69469ed71\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ColorRGBMaterialSlot\",\n    \"m_ObjectId\":
    \"1588c1c643614c23b6aee219871a8ee3\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Base Color\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"BaseColor\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": {\n        \"x\":
    0.5,\n        \"y\": 0.5,\n        \"z\": 0.5\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": [],\n    \"m_ColorMode\": 0,\n    \"m_DefaultColor\": {\n       
    \"r\": 0.5,\n        \"g\": 0.5,\n        \"b\": 0.5,\n        \"a\": 1.0\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n   
    \"m_ObjectId\": \"163d50f08bf34069846e3a89e14e76b6\",\n    \"m_Group\": {\n       
    \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n    },\n    \"m_Name\": \"Multiply\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1177.333251953125,\n           
    \"y\": -439.333251953125,\n            \"width\": 209.3333740234375,\n           
    \"height\": 304.00006103515627\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"eb779c73a66a4fb7a84b4cfba7caba50\"\n        },\n       
    {\n            \"m_Id\": \"bab55667ef2e4b7b8d2746678a23f9c0\"\n        },\n       
    {\n            \"m_Id\": \"6a8eb6fb13b5415c80295dc180738a53\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DInputMaterialSlot\",\n   
    \"m_ObjectId\": \"1beb2d30c6fc4b6bb50c2cd8f7dde451\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Texture\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Texture\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\":
    false,\n    \"m_Texture\": {\n        \"m_SerializedTexture\": \"\",\n       
    \"m_Guid\": \"\"\n    },\n    \"m_DefaultType\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ScreenPositionNode\",\n    \"m_ObjectId\":
    \"1e1c9da091f747e5b7c761f36fa71107\",\n    \"m_Group\": {\n        \"m_Id\":
    \"67f39673beae40e4a48012d83b2fb7d3\"\n    },\n    \"m_Name\": \"Screen Position\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -924.0,\n            \"y\":
    1141.3333740234375,\n            \"width\": 209.3333740234375,\n            \"height\":
    316.0\n        }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\":
    \"0d778a62c57945a3ba410f2e577d9081\"\n        }\n    ],\n    \"synonyms\": [],\n   
    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_ScreenSpaceType\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\": \"1e86dfc1e6264ce486e269c1140393a9\",\n   
    \"m_Title\": \"Dot Product Calculation\",\n    \"m_Position\": {\n        \"x\":
    -928.0,\n        \"y\": -500.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\": \"2308ca3c36a14d56828557014e331c5e\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"NormalMap\",\n    \"m_SlotType\": 1,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"NormalMap\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\":
    []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ColorRGBAMaterialSlot\",\n   
    \"m_ObjectId\": \"256ae4542e644041b8a0bf8529ffea42\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Sprite Mask\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"SpriteMask\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\":
    1.0,\n        \"w\": 1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\":
    []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"2607fa8019a14091bb2e462c37bb8f70\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.UVMaterialSlot\",\n    \"m_ObjectId\":
    \"2653ea511ced4957b8feac2e00a8e6c1\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"UV\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"UV\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0\n    },\n    \"m_Labels\": [],\n    \"m_Channel\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"29a38e11d87846069909f74c0f2dfec3\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"29b1c5e4cbd846159a713db18fe65a11\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Alpha\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Alpha\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 1.0,\n    \"m_DefaultValue\":
    1.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"29def6cee4904afca89d6294f040716d\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"2a39df58aca143bca7e47d94d16cef44\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"VertexDescription.Tangent\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 830.66650390625,\n            \"y\": 712.6666870117188,\n           
    \"width\": 200.0003662109375,\n            \"height\": 42.6666259765625\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"84c18de934e7407b91c7cc88a5cf3f03\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Tangent\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Texture2DInputMaterialSlot\",\n    \"m_ObjectId\":
    \"2cae0f95f0b049bfa6d8fd897f1c61af\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"Texture\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Texture\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\": false,\n   
    \"m_Texture\": {\n        \"m_SerializedTexture\": \"\",\n        \"m_Guid\":
    \"\"\n    },\n    \"m_DefaultType\": 3\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\":
    \"2d2f999e72f942eba3d705921f69eec6\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"e00\": 0.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 0.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 0.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"e00\": 1.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 1.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 1.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DMaterialSlot\",\n   
    \"m_ObjectId\": \"2e65a22e98f44a07b01774ab48c9d584\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"NormalMap\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_BareResource\": false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"2fa9bb0a12584d23acbb20780e6ed037\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 1,\n   
    \"m_Type\": \"UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget\",\n   
    \"m_ObjectId\": \"354209da57124c9c86f6dde3e094ed2d\",\n    \"m_Datas\": [],\n   
    \"m_ActiveSubTarget\": {\n        \"m_Id\": \"84cce720534040beba966bb0315d2d54\"\n   
    },\n    \"m_AllowMaterialOverride\": false,\n    \"m_SurfaceType\": 0,\n    \"m_ZTestMode\":
    4,\n    \"m_ZWriteControl\": 0,\n    \"m_AlphaMode\": 0,\n    \"m_RenderFace\":
    2,\n    \"m_AlphaClip\": false,\n    \"m_CastShadows\": true,\n    \"m_ReceiveShadows\":
    true,\n    \"m_DisableTint\": false,\n    \"m_AdditionalMotionVectorMode\": 0,\n   
    \"m_AlembicMotionVectors\": false,\n    \"m_SupportsLODCrossFade\": false,\n   
    \"m_CustomEditorGUI\": \"\",\n    \"m_SupportVFX\": false\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"368c5e99427d4208af55b21b7fb12b34\",\n    \"m_Id\": 7,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.SubGraphNode\",\n    \"m_ObjectId\": \"3836ae6cdda2414ca42dcd89667aa9bb\",\n   
    \"m_Group\": {\n        \"m_Id\": \"fb495be9e9a54fb7b4aa14ac604610b2\"\n    },\n   
    \"m_Name\": \"SubGraph_BaseSprite\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": -528.6665649414063,\n            \"y\": 391.33331298828127,\n           
    \"width\": 245.99993896484376,\n            \"height\": 353.333251953125\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"726a74eced724d1599b1eb8e8b11e1d4\"\n       
    },\n        {\n            \"m_Id\": \"b115794a87a34c1da3ce875e11bfe744\"\n       
    },\n        {\n            \"m_Id\": \"0ab9ae7a6a6d4841b319b52ded49d746\"\n       
    },\n        {\n            \"m_Id\": \"bf77d739eab34ac9b3c5a2ef61bbb3ad\"\n       
    },\n        {\n            \"m_Id\": \"0d0030026bbc4d5cb6f0874b216e7943\"\n       
    },\n        {\n            \"m_Id\": \"8747dbb9e70f4bfdb08ef2be491aeee6\"\n       
    },\n        {\n            \"m_Id\": \"2308ca3c36a14d56828557014e331c5e\"\n       
    },\n        {\n            \"m_Id\": \"db5fe0aa6acc43579e9fdc4f6d45b6da\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedSubGraph\":
    \"{\\n    \\\"subGraph\\\": {\\n        \\\"fileID\\\": -5475051401550479605,\\n       
    \\\"guid\\\": \\\"75b3da59654c07348bdae8c34f9defae\\\",\\n        \\\"type\\\":
    3\\n    }\\n}\",\n    \"m_PropertyGuids\": [\n        \"dea504ee-c74c-483d-b7ba-1317e1ec12b7\",\n       
    \"a78a8cfb-54cb-49f2-a251-cd222774a166\",\n        \"21a170e7-f132-49d8-b04c-63b1c25853ab\",\n       
    \"194923b9-b055-46fc-baff-e08a00980cff\"\n    ],\n    \"m_PropertyIds\": [\n       
    916035060,\n        -1441464907,\n        1917425063,\n        710538326\n   
    ],\n    \"m_Dropdowns\": [],\n    \"m_DropdownSelectedEntries\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"3c5a5dbd30a24f9b89b662c3da3fe634\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 1.0,\n        \"y\": 1.0,\n       
    \"z\": 1.0,\n        \"w\": 1.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"3daf9d92d1f648fea2cdf9f3ef3cb4aa\",\n    \"m_Id\": 4,\n    \"m_DisplayName\":
    \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"R\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DotProductNode\",\n    \"m_ObjectId\": \"3f9a12f4d77f4c79815c060e93cd6763\",\n   
    \"m_Group\": {\n        \"m_Id\": \"1e86dfc1e6264ce486e269c1140393a9\"\n    },\n   
    \"m_Name\": \"Dot Product\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": -244.0,\n            \"y\": -306.66644287109377,\n            \"width\":
    209.33340454101563,\n            \"height\": 303.99981689453127\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"0f2c1f24ce374126b96e69264c451e08\"\n       
    },\n        {\n            \"m_Id\": \"e87ba52e9bbc4dbca5b879ee32443c88\"\n       
    },\n        {\n            \"m_Id\": \"7a6049f6c5714541881e3033e3401f9c\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"scalar product\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"3fa392ff21304805969eaa5cdd2a4337\",\n    \"m_Id\": 4,\n   
    \"m_DisplayName\": \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"R\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"400da3e7a5c541d7a611b587c4bf1fba\",\n    \"m_Group\": {\n        \"m_Id\":
    \"fb495be9e9a54fb7b4aa14ac604610b2\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -808.0,\n            \"y\":
    474.6666564941406,\n            \"width\": 153.33343505859376,\n            \"height\":
    35.99993896484375\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"5fcf0f6e6dbe43dd8d6d44ebf05f406a\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"fb6c2c0d2b694b4b90c76789f3eb2818\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"40766a93ed4940e9a82cae4a0ca4ccc4\",\n    \"m_Group\": {\n       
    \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 433.9999694824219,\n           
    \"y\": -84.66663360595703,\n            \"width\": 141.33340454101563,\n           
    \"height\": 36.00002670288086\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"d684cab935ec4bf791db36417691034b\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"af0425805a614ca19323990aff0904d4\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"422c106d6e3147f8a179a47bd61a9433\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 10.0,\n        \"e01\": 2.0,\n        \"e02\": 2.0,\n       
    \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n        \"e12\":
    2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\": 2.0,\n       
    \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n        \"e31\":
    2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"42713f51278b4eb99626c00cea7c1739\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"R\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"42901ad621f949c889082c92b173eae8\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\":
    \"44ec89fb93ca48e197ff26c7af0ccce7\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"RGBA\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"RGBA\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"47872200e7d740ef9c9aad08a5a9850e\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.UVMaterialSlot\",\n    \"m_ObjectId\": \"495fe2225de14225b9bd0e45541a9d2d\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"UV\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"UV\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_Labels\": [],\n   
    \"m_Channel\": 1\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SampleTexture2DNode\",\n   
    \"m_ObjectId\": \"4d2603e022d94134a01ca2b20d5646e0\",\n    \"m_Group\": {\n       
    \"m_Id\": \"67f39673beae40e4a48012d83b2fb7d3\"\n    },\n    \"m_Name\": \"Sample
    Texture 2D\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -628.6666259765625,\n            \"y\": 1544.0,\n            \"width\": 209.33331298828126,\n           
    \"height\": 438.666748046875\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"ee578a9f5b7f40e3bcee3e54415eaefc\"\n        },\n       
    {\n            \"m_Id\": \"3daf9d92d1f648fea2cdf9f3ef3cb4aa\"\n        },\n       
    {\n            \"m_Id\": \"f0ba34afbac44b309f08fab667a4587d\"\n        },\n       
    {\n            \"m_Id\": \"6a94b020eb004ef89efc7c1edc559159\"\n        },\n       
    {\n            \"m_Id\": \"853e50ab1b3c4c9a9332cca1cc68956f\"\n        },\n       
    {\n            \"m_Id\": \"710f54e551654276ad8f773d330151e1\"\n        },\n       
    {\n            \"m_Id\": \"495fe2225de14225b9bd0e45541a9d2d\"\n        },\n       
    {\n            \"m_Id\": \"f70672aff7974badbaf1939481adbcfe\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_TextureType\": 0,\n   
    \"m_NormalMapSpace\": 0,\n    \"m_EnableGlobalMipBias\": true,\n    \"m_MipSamplingMode\":
    0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"53e64b3f3b2041ceb08190834655a2d4\",\n    \"m_Id\": 5,\n   
    \"m_DisplayName\": \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"G\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"558a770458e14f7f8eb071f53d0f48da\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.PositionMaterialSlot\",\n    \"m_ObjectId\":
    \"55ae4d2eaee0478ab9ea6da84de39934\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Position\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Position\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"55bea76e3d424776a968e2d1376eddfb\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"VertexDescription.Normal\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 63.33351516723633,\n           
    \"y\": -438.6667175292969,\n            \"width\": 199.99989318847657,\n           
    \"height\": 42.666473388671878\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"6496663e823942e9b9556960465926ad\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Normal\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.SaturateNode\",\n    \"m_ObjectId\": \"5637204faa3141a7a4475a6dfcc30ffa\",\n   
    \"m_Group\": {\n        \"m_Id\": \"1e86dfc1e6264ce486e269c1140393a9\"\n    },\n   
    \"m_Name\": \"Saturate\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    39.333335876464847,\n            \"y\": -306.66644287109377,\n            \"width\":
    209.33340454101563,\n            \"height\": 279.9999084472656\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"2fa9bb0a12584d23acbb20780e6ed037\"\n       
    },\n        {\n            \"m_Id\": \"c232c5f957d74903ab1b2dc581cd8824\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"clamp\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"59b9da91deab448e914ea90438ad78a9\",\n    \"m_Id\": 4,\n   
    \"m_DisplayName\": \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"R\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DMaterialSlot\",\n    \"m_ObjectId\":
    \"5fcf0f6e6dbe43dd8d6d44ebf05f406a\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"MaskTexture\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\": false\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n   
    \"m_ObjectId\": \"60567048a2ed4afaa0314d44a12f454d\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"LightDirection\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n   
    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"633f00a0286c40ce935b0c525fef8879\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"63e5217537eb4843a7985d9559b5c7e6\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.NormalMaterialSlot\",\n    \"m_ObjectId\":
    \"6496663e823942e9b9556960465926ad\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Normal\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Normal\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.SamplerStateMaterialSlot\",\n    \"m_ObjectId\": \"64cf3fa17ed5472cb6a11f9d9acc25db\",\n   
    \"m_Id\": 3,\n    \"m_DisplayName\": \"Sampler\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Sampler\",\n    \"m_StageCapability\":
    3,\n    \"m_BareResource\": false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"6556c74e791348e2a1b09112090f6dd9\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\":
    2.0,\n        \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n       
    \"e12\": 2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\":
    2.0,\n        \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n       
    \"e31\": 2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"66d9934af61f4ea5bcfa197e44e8187a\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.BaseColor\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"1588c1c643614c23b6aee219871a8ee3\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.BaseColor\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"67730b099e5c43a4a09fdcbc0b8ef918\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\":
    \"67f39673beae40e4a48012d83b2fb7d3\",\n    \"m_Title\": \"2D Lighting\",\n   
    \"m_Position\": {\n        \"x\": -949.0,\n        \"y\": 939.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"6a8eb6fb13b5415c80295dc180738a53\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"6a94b020eb004ef89efc7c1edc559159\",\n    \"m_Id\": 6,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"6aed059734ae4d34a59e08e69469ed71\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\":
    0.0,\n        \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n       
    \"e12\": 0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\":
    0.0,\n        \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n       
    \"e31\": 0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"6e3c3349e414456099bb05eb4adeda04\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DMaterialSlot\",\n    \"m_ObjectId\":
    \"6fb94cacc14542e4899405e1ac82cdd4\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\": false\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DInputMaterialSlot\",\n   
    \"m_ObjectId\": \"710f54e551654276ad8f773d330151e1\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Texture\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Texture\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\":
    false,\n    \"m_Texture\": {\n        \"m_SerializedTexture\": \"\",\n       
    \"m_Guid\": \"\"\n    },\n    \"m_DefaultType\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"71fe25b9dc864946944bbff3db84d631\",\n    \"m_Id\": 6,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Texture2DInputMaterialSlot\",\n    \"m_ObjectId\":
    \"726a74eced724d1599b1eb8e8b11e1d4\",\n    \"m_Id\": 916035060,\n    \"m_DisplayName\":
    \"Sprite Texture\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"_Sprite_Texture\",\n    \"m_StageCapability\": 2,\n    \"m_BareResource\":
    false,\n    \"m_Texture\": {\n        \"m_SerializedTexture\": \"\",\n       
    \"m_Guid\": \"\"\n    },\n    \"m_DefaultType\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"79f327fb731647578c0a02c9b2ea6b64\",\n    \"m_Id\": 5,\n    \"m_DisplayName\":
    \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"G\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"7a6049f6c5714541881e3033e3401f9c\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DMaterialSlot\",\n   
    \"m_ObjectId\": \"7c59af06bc6743b39642258dfded61e9\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"MainTex\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\":
    false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.UVNode\",\n   
    \"m_ObjectId\": \"7c74a537cefb4b778973a216c526ffa4\",\n    \"m_Group\": {\n       
    \"m_Id\": \"1e86dfc1e6264ce486e269c1140393a9\"\n    },\n    \"m_Name\": \"UV\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -903.3331909179688,\n           
    \"y\": -349.99981689453127,\n            \"width\": 209.3333740234375,\n           
    \"height\": 315.99993896484377\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"2607fa8019a14091bb2e462c37bb8f70\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"texcoords\",\n        \"coords\",\n       
    \"coordinates\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_OutputChannel\": 0\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ExponentialNode\",\n   
    \"m_ObjectId\": \"7d45750db9644ead88c1b2075195ede7\",\n    \"m_Group\": {\n       
    \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n    },\n    \"m_Name\": \"Exponential\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1177.333251953125,\n           
    \"y\": -90.66657257080078,\n            \"width\": 209.3333740234375,\n           
    \"height\": 315.9999694824219\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"dc17e970e7f3435493657419e64a1afa\"\n        },\n       
    {\n            \"m_Id\": \"29a38e11d87846069909f74c0f2dfec3\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_ExponentialBase\": 1\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PowerNode\",\n   
    \"m_ObjectId\": \"7da15f277c9e42d197fde57875d195bb\",\n    \"m_Group\": {\n       
    \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n    },\n    \"m_Name\": \"Power\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1913.9998779296875,\n           
    \"y\": -287.33331298828127,\n            \"width\": 209.3336181640625,\n           
    \"height\": 303.9999694824219\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"0bd1ca0cc55a4fb58b71f3df3949575d\"\n        },\n       
    {\n            \"m_Id\": \"d875e2cd73d445c4941344f3c489ed8c\"\n        },\n       
    {\n            \"m_Id\": \"ab882293413a4be79a76f96b7a7a4816\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"7db8956a8ca14ed18efc79062b78d6bf\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.NormalTS\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"abb30d11ec734e8981a7dc02827c4a56\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.NormalTS\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\": \"7ee467498251407c9b5a72eb56e6197c\",\n   
    \"m_Group\": {\n        \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n    },\n   
    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    644.0,\n            \"y\": -84.66663360595703,\n            \"width\": 209.3333740234375,\n           
    \"height\": 303.9999694824219\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"2d2f999e72f942eba3d705921f69eec6\"\n        },\n       
    {\n            \"m_Id\": \"422c106d6e3147f8a179a47bd61a9433\"\n        },\n       
    {\n            \"m_Id\": \"fc9079057428443da3c8a98098386de9\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.UVMaterialSlot\",\n    \"m_ObjectId\":
    \"81f1272a739d471d884da9fe5d63b797\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"UV\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"UV\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0\n    },\n    \"m_Labels\": [],\n    \"m_Channel\": 1\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.TangentMaterialSlot\",\n    \"m_ObjectId\":
    \"84c18de934e7407b91c7cc88a5cf3f03\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Tangent\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Tangent\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.Rendering.Universal.ShaderGraph.UniversalSpriteCustomLitSubTarget\",\n   
    \"m_ObjectId\": \"84cce720534040beba966bb0315d2d54\"\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"853e50ab1b3c4c9a9332cca1cc68956f\",\n    \"m_Id\": 7,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\": \"8747dbb9e70f4bfdb08ef2be491aeee6\",\n   
    \"m_Id\": 4,\n    \"m_DisplayName\": \"MaskMap\",\n    \"m_SlotType\": 1,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"MaskMap\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\":
    []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"882338fe623443958bcfbccfd33a74f5\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"G\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"8914c9a794ce454b8f32352a90ef1237\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"8b10fc3031e741f5980d23d4cbdcdb0b\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DMaterialSlot\",\n   
    \"m_ObjectId\": \"8c114af31fe348be8a805200e6c2d503\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"NormalMap\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_BareResource\": false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"8ca8dacc5fd347798114604538a28472\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"8e8ed997226344a5b24d0dd911ad7835\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.SpriteMask\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"256ae4542e644041b8a0bf8529ffea42\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.SpriteMask\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.AddNode\",\n    \"m_ObjectId\": \"8f13a789650041528a35fc7c04d448eb\",\n   
    \"m_Group\": {\n        \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n    },\n   
    \"m_Name\": \"Add\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    919.9999389648438,\n            \"y\": -95.99991607666016,\n            \"width\":
    209.33331298828126,\n            \"height\": 303.9999694824219\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"a1c9ed989ac9437db0065991311968c2\"\n       
    },\n        {\n            \"m_Id\": \"0844f1ee1e754f8cbf3f4eefc21cc640\"\n       
    },\n        {\n            \"m_Id\": \"8b10fc3031e741f5980d23d4cbdcdb0b\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n       
    \"plus\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"924e141cc7bf4dd6a378c10407f0641b\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"92713d3079714e8488fb407a350ceb4f\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.AbsoluteNode\",\n    \"m_ObjectId\": \"972d0289dbef4be9aca67cb7dbf57258\",\n   
    \"m_Group\": {\n        \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n    },\n   
    \"m_Name\": \"Absolute\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    1541.3331298828125,\n            \"y\": -415.3332214355469,\n            \"width\":
    209.3333740234375,\n            \"height\": 280.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"558a770458e14f7f8eb071f53d0f48da\"\n       
    },\n        {\n            \"m_Id\": \"8914c9a794ce454b8f32352a90ef1237\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"positive\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Texture2DShaderProperty\",\n   
    \"m_ObjectId\": \"9934602af1e24eceb2ef05e93ab199a3\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"282ddfed-a250-4376-926e-ef00350e7ce6\"\n    },\n    \"m_Name\":
    \"MainTex\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"MainTex\",\n    \"m_DefaultReferenceName\": \"_MainTex\",\n    \"m_OverrideReferenceName\":
    \"_MainTex\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_PerRendererData\": false,\n    \"m_customAttributes\":
    [],\n    \"m_Value\": {\n        \"m_SerializedTexture\": \"{\\\"texture\\\":{\\\"fileID\\\":2800000,\\\"guid\\\":\\\"0e7a33b2f6906244381dfc5ec519bd28\\\",\\\"type\\\":3}}\",\n       
    \"m_Guid\": \"\"\n    },\n    \"isMainTexture\": false,\n    \"useTilingAndOffset\":
    false,\n    \"useTexelSize\": true,\n    \"m_Modifiable\": true,\n    \"m_DefaultType\":
    0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"9bb0020c39394cd88e61ae4c6331dbcc\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.VertexColorNode\",\n    \"m_ObjectId\":
    \"9d56429c130342b186f3652eda93339f\",\n    \"m_Group\": {\n        \"m_Id\":
    \"11c37652171245909af803f18ede41b4\"\n    },\n    \"m_Name\": \"Vertex Color\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 853.3333740234375,\n           
    \"y\": -439.333251953125,\n            \"width\": 209.3333740234375,\n           
    \"height\": 280.0\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"3c5a5dbd30a24f9b89b662c3da3fe634\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 2,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"9df0f204c3294cecb5d6c4990394f87d\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.ScreenPositionNode\",\n    \"m_ObjectId\":
    \"a0e9ee09913344f79f6b8c34debe29ec\",\n    \"m_Group\": {\n        \"m_Id\":
    \"67f39673beae40e4a48012d83b2fb7d3\"\n    },\n    \"m_Name\": \"Screen Position\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -882.6666870117188,\n           
    \"y\": 1638.6666259765625,\n            \"width\": 209.33331298828126,\n           
    \"height\": 316.0001220703125\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"63e5217537eb4843a7985d9559b5c7e6\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_ScreenSpaceType\": 0\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"a1c9ed989ac9437db0065991311968c2\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n    \"m_ObjectId\": \"a58e4571017b496583fe5f75468ffd53\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Add\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 2549.333251953125,\n           
    \"y\": 123.33329772949219,\n            \"width\": 209.33349609375,\n           
    \"height\": 304.0\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"633f00a0286c40ce935b0c525fef8879\"\n        },\n        {\n           
    \"m_Id\": \"a720b9c406914c62b49d5eb3eb832d13\"\n        },\n        {\n           
    \"m_Id\": \"42901ad621f949c889082c92b173eae8\"\n        }\n    ],\n    \"synonyms\":
    [\n        \"addition\",\n        \"sum\",\n        \"plus\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.AbsoluteNode\",\n   
    \"m_ObjectId\": \"a5971218945548d69f32ea004a932cc1\",\n    \"m_Group\": {\n       
    \"m_Id\": \"11c37652171245909af803f18ede41b4\"\n    },\n    \"m_Name\": \"Absolute\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1541.3331298828125,\n           
    \"y\": -72.00001525878906,\n            \"width\": 209.3333740234375,\n           
    \"height\": 280.00006103515627\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"9df0f204c3294cecb5d6c4990394f87d\"\n        },\n       
    {\n            \"m_Id\": \"a8eeb82c605042aea12396c3f16e1871\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"positive\"\n    ],\n    \"m_Precision\": 0,\n   
    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"a720b9c406914c62b49d5eb3eb832d13\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DMaterialSlot\",\n    \"m_ObjectId\":
    \"a737548681f2494ca2bf94b5c6ca44b6\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\": false\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SaturateNode\",\n   
    \"m_ObjectId\": \"a74090d898a24880917449f7e9d02f40\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Saturate\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 659.3333129882813,\n            \"y\": 1489.9998779296875,\n           
    \"width\": 209.3333740234375,\n            \"height\": 280.0001220703125\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"9bb0020c39394cd88e61ae4c6331dbcc\"\n       
    },\n        {\n            \"m_Id\": \"67730b099e5c43a4a09fdcbc0b8ef918\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"clamp\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"a8eeb82c605042aea12396c3f16e1871\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"ab882293413a4be79a76f96b7a7a4816\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.NormalMaterialSlot\",\n   
    \"m_ObjectId\": \"abb30d11ec734e8981a7dc02827c4a56\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Normal (Tangent Space)\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"NormalTS\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": [],\n    \"m_Space\": 3\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SampleTexture2DNode\",\n    \"m_ObjectId\":
    \"aed27217980a4f229a6059edd1f526ea\",\n    \"m_Group\": {\n        \"m_Id\":
    \"1e86dfc1e6264ce486e269c1140393a9\"\n    },\n    \"m_Name\": \"Sample Texture
    2D\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\":
    {\n            \"serializedVersion\": \"2\",\n            \"x\": -680.66650390625,\n           
    \"y\": -441.33319091796877,\n            \"width\": 209.33319091796876,\n           
    \"height\": 438.66656494140627\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"ed5fac72e27943e792e0058abeb211c3\"\n        },\n       
    {\n            \"m_Id\": \"3fa392ff21304805969eaa5cdd2a4337\"\n        },\n       
    {\n            \"m_Id\": \"53e64b3f3b2041ceb08190834655a2d4\"\n        },\n       
    {\n            \"m_Id\": \"da6d4e3994aa44a284ad39c359300852\"\n        },\n       
    {\n            \"m_Id\": \"b93a4ee399544741912f595cb85540dc\"\n        },\n       
    {\n            \"m_Id\": \"2cae0f95f0b049bfa6d8fd897f1c61af\"\n        },\n       
    {\n            \"m_Id\": \"2653ea511ced4957b8feac2e00a8e6c1\"\n        },\n       
    {\n            \"m_Id\": \"0cf3aee95a9a478f8e0b6b26386b051c\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_TextureType\": 1,\n   
    \"m_NormalMapSpace\": 0,\n    \"m_EnableGlobalMipBias\": true,\n    \"m_MipSamplingMode\":
    0\n}\n\n{\n    \"m_SGVersion\": 1,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty\",\n   
    \"m_ObjectId\": \"af0425805a614ca19323990aff0904d4\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"b1d341f4-5085-4075-94be-aa84a4e071ae\"\n    },\n    \"m_Name\":
    \"Smoothness\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"Smoothness\",\n    \"m_DefaultReferenceName\": \"_Smoothness\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_PerRendererData\": false,\n    \"m_customAttributes\":
    [],\n    \"m_Value\": 0.0,\n    \"m_FloatType\": 0,\n    \"m_RangeValues\": {\n       
    \"x\": 0.0,\n        \"y\": 1.0\n    },\n    \"m_SliderType\": 0,\n    \"m_SliderPower\":
    3.0,\n    \"m_EnumType\": 0,\n    \"m_CSharpEnumString\": \"\",\n    \"m_EnumNames\":
    [\n        \"Default\"\n    ],\n    \"m_EnumValues\": [\n        0\n    ]\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DInputMaterialSlot\",\n   
    \"m_ObjectId\": \"b115794a87a34c1da3ce875e11bfe744\",\n    \"m_Id\": -1441464907,\n   
    \"m_DisplayName\": \"Normal Map\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"_Normal_Map\",\n    \"m_StageCapability\":
    2,\n    \"m_BareResource\": false,\n    \"m_Texture\": {\n        \"m_SerializedTexture\":
    \"\",\n        \"m_Guid\": \"\"\n    },\n    \"m_DefaultType\": 0\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"b1f91ad7f45c4ce2bc7b6a522887814e\",\n    \"m_Group\": {\n       
    \"m_Id\": \"1e86dfc1e6264ce486e269c1140393a9\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -433.9999084472656,\n           
    \"y\": -127.99978637695313,\n            \"width\": 151.33331298828126,\n           
    \"height\": 35.99982452392578\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"60567048a2ed4afaa0314d44a12f454d\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 1,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"f1d48354f1404fc8b1054cf3de4534a8\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Texture2DShaderProperty\",\n   
    \"m_ObjectId\": \"b633a40b3dac40c1b125301b132ca4bf\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"9cead83d-655f-463e-abfb-773ec585496a\"\n    },\n    \"m_Name\":
    \"NormalMap\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"NormalMap\",\n    \"m_DefaultReferenceName\": \"_NormalMap\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_PerRendererData\": false,\n    \"m_customAttributes\":
    [],\n    \"m_Value\": {\n        \"m_SerializedTexture\": \"{\\\"texture\\\":{\\\"fileID\\\":2800000,\\\"guid\\\":\\\"85745efcf54e8714a9166f77dcee24fe\\\",\\\"type\\\":3}}\",\n       
    \"m_Guid\": \"\"\n    },\n    \"isMainTexture\": false,\n    \"useTilingAndOffset\":
    false,\n    \"useTexelSize\": true,\n    \"m_Modifiable\": true,\n    \"m_DefaultType\":
    3\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n   
    \"m_ObjectId\": \"b76b9fadfecc478287c52d3e9e822e63\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Add\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 1015.9999389648438,\n            \"y\": 1150.6666259765625,\n           
    \"width\": 209.33319091796876,\n            \"height\": 304.0\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"92713d3079714e8488fb407a350ceb4f\"\n       
    },\n        {\n            \"m_Id\": \"be4bf4545e15431dac71cdb70e132a90\"\n       
    },\n        {\n            \"m_Id\": \"29def6cee4904afca89d6294f040716d\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n       
    \"plus\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"b93a4ee399544741912f595cb85540dc\",\n    \"m_Id\": 7,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"bab55667ef2e4b7b8d2746678a23f9c0\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\":
    2.0,\n        \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n       
    \"e12\": 2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\":
    2.0,\n        \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n       
    \"e31\": 2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"be4bf4545e15431dac71cdb70e132a90\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"bea831570c964bdc8e8689adde11b573\",\n   
    \"m_Group\": {\n        \"m_Id\": \"1e86dfc1e6264ce486e269c1140393a9\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -885.9998779296875,\n            \"y\": -425.3331298828125,\n            \"width\":
    135.3333740234375,\n            \"height\": 35.99993896484375\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"8c114af31fe348be8a805200e6c2d503\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"b633a40b3dac40c1b125301b132ca4bf\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SampleTexture2DNode\",\n    \"m_ObjectId\":
    \"bf28280b984e4455936fc77ade09cd98\",\n    \"m_Group\": {\n        \"m_Id\":
    \"67f39673beae40e4a48012d83b2fb7d3\"\n    },\n    \"m_Name\": \"Sample Texture
    2D\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\":
    {\n            \"serializedVersion\": \"2\",\n            \"x\": -631.3333740234375,\n           
    \"y\": 997.9998168945313,\n            \"width\": 209.33340454101563,\n           
    \"height\": 438.66693115234377\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"44ec89fb93ca48e197ff26c7af0ccce7\"\n        },\n       
    {\n            \"m_Id\": \"59b9da91deab448e914ea90438ad78a9\"\n        },\n       
    {\n            \"m_Id\": \"79f327fb731647578c0a02c9b2ea6b64\"\n        },\n       
    {\n            \"m_Id\": \"71fe25b9dc864946944bbff3db84d631\"\n        },\n       
    {\n            \"m_Id\": \"368c5e99427d4208af55b21b7fb12b34\"\n        },\n       
    {\n            \"m_Id\": \"1beb2d30c6fc4b6bb50c2cd8f7dde451\"\n        },\n       
    {\n            \"m_Id\": \"81f1272a739d471d884da9fe5d63b797\"\n        },\n       
    {\n            \"m_Id\": \"64cf3fa17ed5472cb6a11f9d9acc25db\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_TextureType\": 0,\n   
    \"m_NormalMapSpace\": 0,\n    \"m_EnableGlobalMipBias\": true,\n    \"m_MipSamplingMode\":
    0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n   
    \"m_ObjectId\": \"bf77d739eab34ac9b3c5a2ef61bbb3ad\",\n    \"m_Id\": 710538326,\n   
    \"m_DisplayName\": \"UV\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"_UV\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"c232c5f957d74903ab1b2dc581cd8824\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"c317623f83aa4fae8a0cc78023526497\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Alpha\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"29b1c5e4cbd846159a713db18fe65a11\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Alpha\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"c85eb7b7cc934be8b3982ebe2fe29d41\",\n   
    \"m_Group\": {\n        \"m_Id\": \"fb495be9e9a54fb7b4aa14ac604610b2\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -802.6665649414063,\n            \"y\": 391.33331298828127,\n            \"width\":
    129.99993896484376,\n            \"height\": 36.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"7c59af06bc6743b39642258dfded61e9\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"9934602af1e24eceb2ef05e93ab199a3\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"d684cab935ec4bf791db36417691034b\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Smoothness\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"d875e2cd73d445c4941344f3c489ed8c\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 2.0,\n        \"y\": 2.0,\n        \"z\": 2.0,\n       
    \"w\": 2.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ColorRGBMaterialSlot\",\n    \"m_ObjectId\":
    \"d91e01373e394166b8e73865c84c2991\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Emission\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Emission\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": {\n        \"x\":
    238.85646057128907,\n        \"y\": 238.85646057128907,\n        \"z\": 238.85646057128907\n   
    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": [],\n    \"m_ColorMode\": 1,\n    \"m_DefaultColor\":
    {\n        \"r\": 0.0,\n        \"g\": 0.0,\n        \"b\": 0.0,\n        \"a\":
    1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"da6d4e3994aa44a284ad39c359300852\",\n    \"m_Id\": 6,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"db5fe0aa6acc43579e9fdc4f6d45b6da\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"Alpha\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Alpha\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"dc17e970e7f3435493657419e64a1afa\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"dd113f9f7f464680aea4f52e0a6e697c\",\n    \"m_Id\": 4,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"de37e72fab8645cf88e1b1df2ac6aa8b\",\n   
    \"m_Group\": {\n        \"m_Id\": \"fb495be9e9a54fb7b4aa14ac604610b2\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -808.0,\n            \"y\": 427.33331298828127,\n            \"width\": 135.3333740234375,\n           
    \"height\": 36.0\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"2e65a22e98f44a07b01774ab48c9d584\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"b633a40b3dac40c1b125301b132ca4bf\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"e0b8095e1b334ef8b8882d06b41093a9\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\": 2.0,\n       
    \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n        \"e12\":
    2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\": 2.0,\n       
    \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n        \"e31\":
    2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.CategoryData\",\n    \"m_ObjectId\":
    \"e605249f5a0a46e6ac17aa01e0de40b5\",\n    \"m_Name\": \"\",\n    \"m_ChildObjectList\":
    [\n        {\n            \"m_Id\": \"9934602af1e24eceb2ef05e93ab199a3\"\n       
    },\n        {\n            \"m_Id\": \"b633a40b3dac40c1b125301b132ca4bf\"\n       
    },\n        {\n            \"m_Id\": \"fb6c2c0d2b694b4b90c76789f3eb2818\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"e67fa66af20940a29d98e1100a835369\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Emission\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"d91e01373e394166b8e73865c84c2991\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Emission\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"e87ba52e9bbc4dbca5b879ee32443c88\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 1.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"eb779c73a66a4fb7a84b4cfba7caba50\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\":
    \"ecaa51a43ef644f1bb29ab7f0505c8ca\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\": \"ed5fac72e27943e792e0058abeb211c3\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"RGBA\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"RGBA\",\n    \"m_StageCapability\": 2,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"ee578a9f5b7f40e3bcee3e54415eaefc\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"RGBA\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"RGBA\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEngine.Experimental.Rendering.Universal.LightTextureNode\",\n   
    \"m_ObjectId\": \"ef8de3a8eb5741279ace1f7127d02e9b\",\n    \"m_Group\": {\n       
    \"m_Id\": \"67f39673beae40e4a48012d83b2fb7d3\"\n    },\n    \"m_Name\": \"2D
    Light Texture\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -892.0,\n            \"y\": 1004.6666259765625,\n            \"width\": 147.33331298828126,\n           
    \"height\": 114.6666259765625\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"a737548681f2494ca2bf94b5c6ca44b6\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_BlendStyle\": 0\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"f0ba34afbac44b309f08fab667a4587d\",\n    \"m_Id\": 5,\n   
    \"m_DisplayName\": \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"G\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    1,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty\",\n   
    \"m_ObjectId\": \"f1d48354f1404fc8b1054cf3de4534a8\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"d080a5ab-bbe4-4c7f-94e3-8cacb6e3d159\"\n    },\n    \"m_Name\":
    \"LightDirection\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"LightDirection\",\n    \"m_DefaultReferenceName\": \"_LightDirection\",\n   
    \"m_OverrideReferenceName\": \"\",\n    \"m_GeneratePropertyBlock\": true,\n   
    \"m_UseCustomSlotLabel\": false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\":
    0,\n    \"m_Precision\": 1,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_PerRendererData\": false,\n    \"m_customAttributes\":
    [],\n    \"m_Value\": {\n        \"x\": 1.6399999856948853,\n        \"y\": 1.9600000381469727,\n       
    \"z\": -0.75,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.SamplerStateMaterialSlot\",\n    \"m_ObjectId\":
    \"f70672aff7974badbaf1939481adbcfe\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"Sampler\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Sampler\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\": false\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n   
    \"m_ObjectId\": \"fb495be9e9a54fb7b4aa14ac604610b2\",\n    \"m_Title\": \"Base
    Sprite Textures\",\n    \"m_Position\": {\n        \"x\": -833.0,\n        \"y\":
    332.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Texture2DShaderProperty\",\n   
    \"m_ObjectId\": \"fb6c2c0d2b694b4b90c76789f3eb2818\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"24233b10-e5d6-467f-8b83-b05997311a55\"\n    },\n    \"m_Name\":
    \"MaskTexture\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"MaskTexture\",\n    \"m_DefaultReferenceName\": \"_MaskTexture\",\n    \"m_OverrideReferenceName\":
    \"_MaskTex\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_PerRendererData\": false,\n    \"m_customAttributes\":
    [],\n    \"m_Value\": {\n        \"m_SerializedTexture\": \"{\\\"texture\\\":{\\\"fileID\\\":2800000,\\\"guid\\\":\\\"5011553f64fba9345b0cc4e306476e17\\\",\\\"type\\\":3}}\",\n       
    \"m_Guid\": \"\"\n    },\n    \"isMainTexture\": false,\n    \"useTilingAndOffset\":
    false,\n    \"useTexelSize\": true,\n    \"m_Modifiable\": true,\n    \"m_DefaultType\":
    0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"fc9079057428443da3c8a98098386de9\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEngine.Experimental.Rendering.Universal.LightTextureNode\",\n   
    \"m_ObjectId\": \"fd7b00e0ff074b69a4736770df2a618c\",\n    \"m_Group\": {\n       
    \"m_Id\": \"67f39673beae40e4a48012d83b2fb7d3\"\n    },\n    \"m_Name\": \"2D
    Light Texture\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -847.3333129882813,\n            \"y\": 1492.666748046875,\n            \"width\":
    147.33331298828126,\n            \"height\": 114.6666259765625\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"6fb94cacc14542e4899405e1ac82cdd4\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_BlendStyle\": 3\n}\n\n"
  m_AssetMaybeChangedOnDisk: 0
  m_AssetMaybeDeleted: 0
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ProjectBrowser
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 627
    width: 967
    height: 306
  m_MinSize: {x: 231, y: 276}
  m_MaxSize: {x: 10001, y: 10026}
  m_ActualView: {fileID: 2}
  m_Panes:
  - {fileID: 2}
  - {fileID: 15}
  - {fileID: 16}
  - {fileID: 17}
  - {fileID: 18}
  - {fileID: 19}
  - {fileID: 20}
  - {fileID: 21}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console
    m_Image: {fileID: -4327648978806127646, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Console\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 706
    width: 966
    height: 280
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac9b4e439aace6446a0642ca97e8f43b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Mobile.AndroidLogcat.Editor::Unity.Android.Logcat.AndroidLogcatConsoleWindow
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Android Logcat
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Android Logcat\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 722
    width: 791
    height: 264
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13956, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: UI Toolkit Debugger
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "UI Toolkit Debugger\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 684
    width: 1461
    height: 302
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_DebuggerImpl:
    m_LastVisualTreeName: 
  m_DebuggerContext:
    m_SelectedElementIndex: 311
    m_ShowLayoutBound: 0
    m_ShowRepaintOverlay: 0
    m_ShowDrawStats: 0
    m_BreakBatches: 0
    m_ShowWireframe: 0
    m_ShowTextureAtlasViewer: 0
    m_ShowTextMetrics: 0
--- !u!114 &18
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12071, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Animation
    m_Image: {fileID: -8166618308981325432, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Animation\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 684
    width: 1445
    height: 302
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_LockTracker:
    m_IsLocked: 0
  m_LastSelectedObjectID: 69960
--- !u!114 &19
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13202, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 1000, y: 500}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Frame Debugger
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Frame Debugger\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 740
    width: 1310
    height: 246
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_TreeWidth: 337.92
  m_TreeViewState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
--- !u!114 &20
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12373, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Audio Mixer
    m_Image: {fileID: 2344599766593239149, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Audio Mixer\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 722
    width: 913
    height: 221
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_MixersTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 41770
    m_ExpandedIDs: 12eb343c
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_LayoutStripsOnTop:
    m_VerticalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 65
      - 35
      relativeSizes:
      - 0.65
      - 0.35000002
      minSizes:
      - 85
      - 105
      maxSizes:
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
    m_HorizontalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 60
      - 60
      - 60
      - 60
      relativeSizes:
      - 0.25
      - 0.25
      - 0.25
      - 0.25
      minSizes:
      - 85
      - 85
      - 85
      - 85
      maxSizes:
      - 0
      - 0
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
  m_LayoutStripsOnRight:
    m_VerticalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 60
      - 60
      - 60
      - 60
      relativeSizes:
      - 0.25
      - 0.25
      - 0.25
      - 0.25
      minSizes:
      - 100
      - 85
      - 85
      - 85
      maxSizes:
      - 0
      - 0
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
    m_HorizontalSplitter:
      ID: 11974
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 274
      - 639
      relativeSizes:
      - 0.3
      - 0.7
      minSizes:
      - 160
      - 160
      maxSizes:
      - 0
      - 0
      lastTotalSize: 913
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
  m_SectionOrder: 00000000030000000100000002000000
  m_LayoutMode: 1
  m_SortGroupsAlphabetically: 0
  m_ShowReferencedBuses: 1
  m_ShowBusConnections: 0
  m_ShowBusConnectionsOfSelection: 0
--- !u!114 &21
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12070, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 900, y: 216}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Profiler
    m_Image: {fileID: -1089619856830078684, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Profiler\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 635
    width: 1322
    height: 351
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Recording: 0
  m_ActiveNativePlatformSupportModuleName: 
  m_AllModules:
  - rid: 4530250837061271552
  - rid: 4530250837061271553
  - rid: 4530250837061271554
  - rid: 4530250837061271555
  - rid: 4530250837061271556
  - rid: 4530250837061271557
  - rid: 4530250837061271558
  - rid: 4530250837061271559
  - rid: 4530250837061271562
  - rid: 4530250837061271563
  - rid: 4530250837061271564
  - rid: 4530250837061271565
  - rid: 4530250837061271566
  - rid: 4530250837061271567
  m_CallstackRecordMode: 1
  m_ClearOnPlay: 1
  references:
    version: 2
    RefIds:
    - rid: 4530250837061271552
      type: {class: CPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.CPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 3
        m_FrameDataHierarchyView:
          m_Serialized: 1
          m_TreeViewState:
            scrollPos: {x: 0, y: 473}
            m_SelectedIDs: e7010000
            m_LastClickedID: 0
            m_ExpandedIDs: 0400000062000000630000004801000050010000e3010000e4010000
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns:
            - width: 799
              sortedAscending: 0
              headerContent:
                m_Text: Overview
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Overview\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 200
              maxWidth: 1000000
              autoResize: 1
              allowToggleVisibility: 0
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Total
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Total\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Calls
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Calls\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: GC Alloc
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "GC Alloc\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Time ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Time ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 25
              sortedAscending: 0
              headerContent:
                m_Text: 
                m_Image: {fileID: -5161429177145976760, guid: 0000000000000000d000000000000000, type: 0}
                m_Tooltip: Warnings
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 25
              maxWidth: 25
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            m_VisibleColumns: 0000000001000000020000000300000004000000050000000600000007000000
            m_SortedColumns: 0000000005000000
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 10938
            splitterInitialOffset: 0
            currentActiveSplitter: -1
            realSizes:
            - 939
            - 402
            relativeSizes:
            - 0.7
            - 0.3
            minSizes:
            - 450
            - 50
            maxSizes:
            - 0
            - 0
            lastTotalSize: 1341
            splitSize: 6
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 403
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 00000000
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns:
              - width: 80
                sortedAscending: 1
                headerContent:
                  m_Text: Unity Object Name
                  m_Image: {fileID: 0}
                  m_Tooltip: 
                  m_TextWithWhitespace: "Unity Object Name\u200B"
                contextMenuText: 
                headerTextAlignment: 0
                sortingArrowAlignment: 2
                minWidth: 60
                maxWidth: 1000000
                autoResize: 1
                allowToggleVisibility: 0
                canSort: 1
                userData: 0
              - width: 80
                sortedAscending: 0
                headerContent:
                  m_Text: Total
                  m_Image: {fileID: 0}
                  m_Tooltip: 
                  m_TextWithWhitespace: "Total\u200B"
                contextMenuText: 
                headerTextAlignment: 0
                sortingArrowAlignment: 2
                minWidth: 50
                maxWidth: 1000000
                autoResize: 0
                allowToggleVisibility: 1
                canSort: 1
                userData: 0
              - width: 80
                sortedAscending: 0
                headerContent:
                  m_Text: GC Alloc
                  m_Image: {fileID: 0}
                  m_Tooltip: 
                  m_TextWithWhitespace: "GC Alloc\u200B"
                contextMenuText: 
                headerTextAlignment: 0
                sortingArrowAlignment: 2
                minWidth: 50
                maxWidth: 1000000
                autoResize: 0
                allowToggleVisibility: 1
                canSort: 1
                userData: 0
              - width: 80
                sortedAscending: 0
                headerContent:
                  m_Text: Time ms
                  m_Image: {fileID: 0}
                  m_Tooltip: 
                  m_TextWithWhitespace: "Time ms\u200B"
                contextMenuText: 
                headerTextAlignment: 0
                sortingArrowAlignment: 2
                minWidth: 50
                maxWidth: 1000000
                autoResize: 0
                allowToggleVisibility: 1
                canSort: 1
                userData: 0
              m_VisibleColumns: 00000000010000000200000003000000
              m_SortedColumns: 03000000
            m_VertSplit:
              ID: 12531
              splitterInitialOffset: 0
              currentActiveSplitter: -1
              realSizes:
              - 317
              - 136
              relativeSizes:
              - 0.7
              - 0.3
              minSizes:
              - 50
              - 50
              maxSizes:
              - 0
              - 0
              lastTotalSize: 453
              splitSize: 6
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 15073
              splitterInitialOffset: 0
              currentActiveSplitter: -1
              realSizes:
              - 127
              - 54
              relativeSizes:
              - 0.7
              - 0.3
              minSizes:
              - 50
              - 50
              maxSizes:
              - 0
              - 0
              lastTotalSize: 181
              splitSize: 6
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 01000000
                m_LastClickedID: 1
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 14652
          <threadIndex>k__BackingField: 0
          m_GroupName: 
    - rid: 4530250837061271553
      type: {class: GPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 0
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns: []
            m_VisibleColumns: 
            m_SortedColumns: 
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: 0
            realSizes: []
            relativeSizes: []
            minSizes: []
            maxSizes: []
            lastTotalSize: 0
            splitSize: 0
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 0
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 4530250837061271554
      type: {class: RenderingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.RenderingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271555
      type: {class: MemoryProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.MemoryProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewSplit:
          ID: 18071
          splitterInitialOffset: 0
          currentActiveSplitter: -1
          realSizes:
          - 557
          - 532
          relativeSizes:
          - 0.5113636
          - 0.48863637
          minSizes:
          - 450
          - 50
          maxSizes:
          - 0
          - 0
          lastTotalSize: 1089
          splitSize: 6
          xOffset: 0
          m_Version: 1
          oldRealSizes: 
          oldMinSizes: 
          oldMaxSizes: 
          oldSplitSize: 0
    - rid: 4530250837061271556
      type: {class: AudioProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AudioProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ShowInactiveDSPChains: 0
        m_HighlightAudibleDSPChains: 1
        m_DSPGraphZoomFactor: 1
        m_DSPGraphHorizontalLayout: 0
    - rid: 4530250837061271557
      type: {class: VideoProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VideoProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271558
      type: {class: PhysicsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.PhysicsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271559
      type: {class: Physics2DProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.Physics2DProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271562
      type: {class: UIProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271563
      type: {class: UIDetailsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIDetailsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271564
      type: {class: GlobalIlluminationProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GlobalIlluminationProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271565
      type: {class: VirtualTexturingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VirtualTexturingProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_VTProfilerView:
          rid: 4530250837061271568
    - rid: 4530250837061271566
      type: {class: FileIOProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.FileIOProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271567
      type: {class: AssetLoadingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AssetLoadingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 4530250837061271568
      type: {class: VirtualTexturingProfilerView, ns: UnityEditor, asm: UnityEditor.CoreModule}
      data:
        m_SortAscending: 0
        m_SortedColumn: -1
--- !u!114 &22
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: InspectorWindow
  m_EditorClassIdentifier: UnityEditor.dll::UnityEditor.DockArea
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 967
    y: 0
    width: 453
    height: 933
  m_MinSize: {x: 277, y: 76}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 23}
  m_Panes:
  - {fileID: 23}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &23
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -2667387946076563598, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Inspector\u200B"
  m_Pos:
    serializedVersion: 2
    x: 967
    y: 79
    width: 451
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: 255
    m_ControlHash: -371814159
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -1
  m_LastVerticalScrollValue: 0
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
--- !u!114 &24
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: GameView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1420
    y: 0
    width: 500
    height: 933
  m_MinSize: {x: 101, y: 126}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 25}
  m_Panes:
  - {fileID: 25}
  - {fileID: 26}
  - {fileID: 27}
  - {fileID: 28}
  - {fileID: 29}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &25
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: -6423792434712278376, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Game\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1420
    y: 79
    width: 499
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SerializedViewNames:
  - UnityEditor.DeviceSimulation.SimulatorWindow
  m_SerializedViewValues:
  - D:\Users\phant\Desktop\Data Recovery\Match2D\Library\PlayModeViewStates\a47a243d87570bf419699a197f3234b5
  m_PlayModeViewName: GameView
  m_ShowGizmos: 0
  m_TargetDisplay: 0
  m_ClearColor: {r: 0, g: 0, b: 0, a: 0}
  m_TargetSize: {x: 1080, y: 1920}
  m_TextureFilterMode: 0
  m_TextureHideFlags: 61
  m_RenderIMGUI: 1
  m_EnterPlayModeBehavior: 0
  m_UseMipMap: 0
  m_VSyncEnabled: 1
  m_Gizmos: 0
  m_Stats: 0
  m_SelectedSizes: 07000000000000000000000005000000000000000000000000000000000000000000000000000000
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    hZoomLockedByDefault: 0
    vZoomLockedByDefault: 0
    m_HBaseRangeMin: -540
    m_HBaseRangeMax: 540
    m_VBaseRangeMin: -960
    m_VBaseRangeMax: 960
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 0
    m_EnableSliderZoomHorizontal: 0
    m_EnableSliderZoomVertical: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 21
      width: 499
      height: 886
    m_Scale: {x: 0.46145836, y: 0.46145833}
    m_Translation: {x: 249.5, y: 443}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -540.6772
      y: -960
      width: 1081.3544
      height: 1920
    m_MinimalGUI: 1
  m_defaultScale: 0.46145833
  m_LastWindowPixelSize: {x: 499, y: 907}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 01000001000000000000
  m_XRRenderMode: 0
  m_RenderTexture: {fileID: 0}
  m_showToolbar: 1
--- !u!114 &26
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 04aa8c76f318e684ab555cc02d7fa815, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::ModernAILevelGeneratorWindow
  m_MinSize: {x: 900, y: 700}
  m_MaxSize: {x: 1400, y: 1000}
  m_TitleContent:
    m_Text: "\U0001F916 AI Level Generator"
    m_Image: {fileID: 0}
    m_Tooltip: Modern AI-powered level generation tool
    m_TextWithWhitespace: "\U0001F916 AI Level Generator\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1162
    y: 79
    width: 757
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  aiGenerator: {fileID: 0}
  generationConfig: {fileID: 11400000, guid: acbb6d2b91e66ee47b44078e72483bc7, type: 2}
  targetLevelNumber: 2
  batchGenerationCount: 10
  nextStartLevel: 1
  autoTrackLevels: 1
  useFastGeneration: 1
  autoDetectNextLevel: 1
--- !u!114 &27
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c68702d6522a48e88cc91a473dbfeb5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.AI.Image::Unity.AI.Image.Windows.TextureGeneratorWindow
  m_MinSize: {x: 240, y: 524}
  m_MaxSize: {x: 3840, y: 2160}
  m_TitleContent:
    m_Text: New Sprite
    m_Image: {fileID: -8170331624776197724, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "New Sprite\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1426
    y: 79
    width: 493
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  assetContext:
    guid: c2358c2d19e3ee44eb9acffbb2889fc3
  isEditorLocked: 1
--- !u!114 &28
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f683d99f24875748a6656966ca9cea0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.2D.Tilemap.Editor::UnityEditor.Tilemaps.GridPaintPaletteWindow
  m_MinSize: {x: 356, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Tile Palette
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Tile Palette\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1421
    y: 79
    width: 498
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 1
      id: Overlays/OverlayMenu
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    m_ContainerData:
    - containerId: overlay-toolbar__top
      scrollOffset: 0
    - containerId: overlay-toolbar__left
      scrollOffset: 0
    - containerId: overlay-container--left
      scrollOffset: 0
    - containerId: overlay-container--right
      scrollOffset: 0
    - containerId: overlay-toolbar__right
      scrollOffset: 0
    - containerId: overlay-toolbar__bottom
      scrollOffset: 0
    - containerId: Floating
      scrollOffset: 0
    m_OverlaysVisible: 1
--- !u!114 &29
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12079, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 390, y: 390}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Lighting
    m_Image: {fileID: -1347227620855488341, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Lighting\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1215
    y: 79
    width: 704
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
