%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 1
    m_PVRFilteringGaussRadiusAO: 1
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 20201, guid: 0000000000000000f000000000000000, type: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &175999378
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 175999381}
  - component: {fileID: 175999380}
  - component: {fileID: 175999379}
  m_Layer: 0
  m_Name: ShowIslandAnimation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &175999379
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175999378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.RenderPipelines.Universal.Runtime::UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
  m_RenderShadows: 0
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: 0
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 0
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
  m_Version: 2
--- !u!20 &175999380
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175999378}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 1, g: 1, b: 1, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 1
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.1
  far clip plane: 100
  field of view: 33
  orthographic: 1
  orthographic size: 5.36
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 256
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 8400000, guid: 115e0bfaec0e5b248b58e4add95a7c8f, type: 2}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 0
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &175999381
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175999378}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.46, y: 0, z: -5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1834221692}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &225474873
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 455206254}
    m_Modifications:
    - target: {fileID: 245581523800709616, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_Name
      value: Shine_blue
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalPosition.x
      value: -15
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
--- !u!4 &225474874 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 245581523800709617, guid: 9442d241d2d8ecc47b49f256b55325c6, type: 3}
  m_PrefabInstance: {fileID: 225474873}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &317917302
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 317917303}
  - component: {fileID: 317917305}
  - component: {fileID: 317917304}
  m_Layer: 0
  m_Name: TilemapBackgorund
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &317917303
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 317917302}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1708267986}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!483693784 &317917304
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 317917302}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: -10
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 0
  m_Mode: 2
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &317917305
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 317917302}
  m_Enabled: 1
  m_Tiles: {}
  m_AnimatedTiles: {}
  m_TileAssetArray: []
  m_TileSpriteArray: []
  m_TileMatrixArray: []
  m_TileColorArray: []
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Origin: {x: 0, y: 0, z: 0}
  m_Size: {x: 0, y: 0, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!1 &322167574
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 322167576}
  - component: {fileID: 322167575}
  m_Layer: 0
  m_Name: GameStateManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &322167575
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 322167574}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 333f1ef9e17e8f3449e99e94276d4975, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::OwnMatch3.GameStateManager
  menuMusic: {fileID: 8300000, guid: a8fe220491e97ad4e9466345005b82fb, type: 3}
  gameplayMusic: {fileID: 8300000, guid: 2b373b2a3b3cacf42add4ba2bb45103e, type: 3}
  musicFadeTime: 1
--- !u!4 &322167576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 322167574}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 3.44141, y: 2.229, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &354792329
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 354792331}
  - component: {fileID: 354792330}
  m_Layer: 0
  m_Name: Light 2D
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &354792330
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 354792329}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 073797afb82c5a1438f328866b10b3f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.RenderPipelines.Universal.2D.Runtime::UnityEngine.Rendering.Universal.Light2D
  m_ComponentVersion: 2
  m_LightType: 4
  m_BlendStyleIndex: 0
  m_FalloffIntensity: 0.5
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1.15
  m_LightVolumeIntensity: 1
  m_LightVolumeEnabled: 0
  m_ApplyToSortingLayers: 5bcfba3300000000b777b170e5ecbabdfdccb827afc02d7c
  m_LightCookieSprite: {fileID: 0}
  m_DeprecatedPointLightCookieSprite: {fileID: 0}
  m_LightOrder: 0
  m_AlphaBlendOnOverlap: 0
  m_OverlapOperation: 0
  m_NormalMapDistance: 3
  m_NormalMapQuality: 2
  m_UseNormalMap: 0
  m_ShadowsEnabled: 1
  m_ShadowIntensity: 0.75
  m_ShadowSoftness: 0.3
  m_ShadowSoftnessFalloffIntensity: 0.5
  m_ShadowVolumeIntensityEnabled: 0
  m_ShadowVolumeIntensity: 0.75
  m_LocalBounds:
    m_Center: {x: 0, y: -0.00000011920929, z: 0}
    m_Extent: {x: 0.9985302, y: 0.99853027, z: 0}
  m_PointLightInnerAngle: 360
  m_PointLightOuterAngle: 360
  m_PointLightInnerRadius: 0
  m_PointLightOuterRadius: 1
  m_ShapeLightParametricSides: 5
  m_ShapeLightParametricAngleOffset: 0
  m_ShapeLightParametricRadius: 1
  m_ShapeLightFalloffSize: 0.5
  m_ShapeLightFalloffOffset: {x: 0, y: 0}
  m_ShapePath:
  - {x: -0.5, y: -0.5, z: 0}
  - {x: 0.5, y: -0.5, z: 0}
  - {x: 0.5, y: 0.5, z: 0}
  - {x: -0.5, y: 0.5, z: 0}
--- !u!4 &354792331
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 354792329}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.7203517, y: -0.022601798, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &404524197
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 404524199}
  - component: {fileID: 404524198}
  m_Layer: 0
  m_Name: CameraSetup
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &404524198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 404524197}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e1f8deee91776f043a53ab83ec56a452, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::CameraManager
  mainCamera: {fileID: 1201193891}
  baseBorderMargin: 0.5
  verticalUIOffset: 0.5
  enableSafeAreaHandling: 1
  safeAreaMargin: 0.19999999
  referenceResolution: {x: 1080, y: 1920}
  adaptToAspectRatio: 1
  showDebugInfo: 0
  enableAutoFit: 1
  autoFitPadding: 0.014
  minOrthographicSize: 2
  maxOrthographicSize: 15
  debugAutoFit: 0
  disableFrameDetection: 0
  useManualOverride: 0
  manualOrthographicSize: 6
  manualCameraOffset: {x: 0, y: 0, z: 0}
--- !u!4 &404524199
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 404524197}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6.73781, y: 4.92275, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &412236353
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 804261073}
    m_Modifications:
    - target: {fileID: 2167446387808179993, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_Layer
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 2167446388347710822, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_Layer
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 2167446388960849892, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_Layer
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.33
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.44
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalPosition.y
      value: -6.46
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalPosition.z
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5518309372605910988, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_Layer
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 7320661712846382290, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_Layer
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 7823327091382468809, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_Name
      value: Area_fire_red
      objectReference: {fileID: 0}
    - target: {fileID: 7823327091382468809, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
      propertyPath: m_Layer
      value: 7
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
--- !u!4 &412236354 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3559932527940391457, guid: af0d06ad2f476cc43be23ac0f464a510, type: 3}
  m_PrefabInstance: {fileID: 412236353}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &420747020
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 420747022}
  - component: {fileID: 420747021}
  - component: {fileID: 420747024}
  - component: {fileID: 420747023}
  m_Layer: 0
  m_Name: DailyReward&Missions
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &420747021
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420747020}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 27d3f556b3df16d45bf7367589245769, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::OwnMatch3.UI.DailyRewardsManager
  uiDocument: {fileID: 2063233940}
  rewardItemTemplate: {fileID: 9197481963319205126, guid: 121cb67493dbe6641b428b0446350448, type: 3}
  rewardCycle:
  - day: 1
    rewardType: 0
    amount: 15
    icon: {fileID: 0}
    description: 
  - day: 2
    rewardType: 0
    amount: 35
    icon: {fileID: 0}
    description: 
  - day: 3
    rewardType: 0
    amount: 65
    icon: {fileID: 0}
    description: 
  - day: 4
    rewardType: 0
    amount: 95
    icon: {fileID: 0}
    description: 
  - day: 5
    rewardType: 0
    amount: 125
    icon: {fileID: 0}
    description: 
  - day: 6
    rewardType: 0
    amount: 500
    icon: {fileID: 0}
    description: 
  - day: 7
    rewardType: 0
    amount: 2750
    icon: {fileID: 0}
    description: 
  debugTimerSpeedMultiplier: 24
--- !u!4 &420747022
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420747020}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.69369, y: 0.51357, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &420747023
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420747020}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 210e05257e710e14182a1d18ac75075b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::OwnMatch3.UI.MissionsUIController
  missionsUIDocument: {fileID: 2063233940}
  missionItemTemplate: {fileID: 9197481963319205126, guid: 017a7187ddf30d24b875794ab9bc9ca8, type: 3}
  panelAnimationDuration: 0.4
  missionItemAnimationDelay: 0.1
--- !u!114 &420747024
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420747020}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2465a88808c1a124ba9b956f110fc904, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::OwnMatch3.UI.MissionsManager
  dailyMissionsCount: 10
  enableMissions: 1
  missionResetHour: 12
  baseGoldReward: 50
  baseStarReward: 1
  rewardMultipliers: 0100000001000000020000000200000003000000030000000400000004000000050000000a000000
--- !u!1 &455206251
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 455206254}
  - component: {fileID: 455206252}
  - component: {fileID: 455206253}
  m_Layer: 0
  m_Name: ShowEndGame
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!20 &455206252
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 455206251}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 1, g: 1, b: 1, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 1
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.1
  far clip plane: 100
  field of view: 33
  orthographic: 1
  orthographic size: 5.36
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 128
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 8400000, guid: 6a24df9ab911ec045a24d79b881d9d8c, type: 2}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 0
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!114 &455206253
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 455206251}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.RenderPipelines.Universal.Runtime::UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
  m_RenderShadows: 0
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: 0
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 0
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
  m_Version: 2
--- !u!4 &455206254
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 455206251}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 225474874}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &464329151
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 464329153}
  - component: {fileID: 464329152}
  m_Layer: 0
  m_Name: AudioManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &464329152
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 464329151}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6be7d808a8cf7384694f47dba6bc9b61, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::OwnMatch3.AudioManager
  menuMusic: {fileID: 8300000, guid: a8fe220491e97ad4e9466345005b82fb, type: 3}
  gameplayMusicTracks:
  - {fileID: 8300000, guid: 2b373b2a3b3cacf42add4ba2bb45103e, type: 3}
  musicSourcePrefab: {fileID: 0}
  musicMixerGroup: {fileID: 6051035814002763456, guid: 6073c46e496651d4484335f4d91fc61c, type: 2}
  musicFadeTime: 1
  randomizeGameplayMusic: 0
--- !u!4 &464329153
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 464329151}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 3.44141, y: 2.229, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &514902271
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 514902275}
  - component: {fileID: 514902274}
  m_Layer: 0
  m_Name: ADS Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &514902274
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 514902271}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fcbf93f224533ac419ea8dc3a90cb165, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::OwnMatch3.Monetization.AdsManager
  adConfig: {fileID: 11400000, guid: d446f6d870ba3aa428442fbbda8227c7, type: 2}
  enableDebugLogs: 1
  autoInitialize: 1
  showBannerOnStart: 0
--- !u!4 &514902275
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 514902271}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -14.79264, y: -2.7656, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &553775152
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 553775155}
  - component: {fileID: 553775154}
  - component: {fileID: 553775153}
  m_Layer: 0
  m_Name: ScoreManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &553775153
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 553775152}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac7dba4a196904a4dae62fcc9e5592eb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::ScoreProgressBarController
  uiDocument: {fileID: 1427688306}
  progressBarName: score-progress-bar
  autoFindUIDocument: 0
  enableDebugLogs: 0
--- !u!114 &553775154
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 553775152}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b21a44165428024498fbea062e25ed61, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::ScoreManager
  baseMatchPoints: 10
  obstacleDestroyPoints: 60
  comboMultiplier: 1.4
  maxComboMultiplier: 3
  pointsPerGoalProgress: 100
  goalCompletionBonus: 500
  useGoalBasedScoring: 0
  enableDebugLogs: 0
--- !u!4 &553775155
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 553775152}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &567975085
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 567975087}
  - component: {fileID: 567975086}
  m_Layer: 0
  m_Name: BonusGemEffects
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &567975086
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567975085}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 371e704ffc374c6468ae5d3d3e6a80ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::BonusGemEffects
  placementScaleMultiplier: 0.5
  placementScaleDuration: 0.1
  placementScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1.0041809
      value: 0.49761963
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  explosionScaleMultiplier: 2
  explosionDuration: 0.5
  explosionCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  bombColor: {r: 1, g: 0, b: 0, a: 1}
  rowClearColor: {r: 0, g: 0, b: 1, a: 1}
  columnClearColor: {r: 0, g: 1, b: 0, a: 1}
  crossClearColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  rainbowColor: {r: 1, g: 0, b: 1, a: 1}
  audioSource: {fileID: 0}
  placementSound: {fileID: 0}
  triggerSound: {fileID: 0}
  explosionSound: {fileID: 0}
--- !u!4 &567975087
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567975085}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &574392696
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 574392698}
  - component: {fileID: 574392697}
  m_Layer: 0
  m_Name: StarRatingAudioSource
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!82 &574392697
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 574392696}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 0.7
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!4 &574392698
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 574392696}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &696858325
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 696858327}
  - component: {fileID: 696858326}
  - component: {fileID: 696858328}
  m_Layer: 0
  m_Name: EndGamePopupController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &696858326
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696858325}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 217f55bd907021c4cb1c35a7a99f41dc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::EndGamePopupController
  uiDocument: {fileID: 1427688306}
  endScreenName: EndScreen
  starContainerName: StarContainers
  scoreTextName: scoreText
  replayButtonName: replayButton
  nextButtonName: nextButton
  menuButtonName: menuButton
  autoFindUIDocument: 0
  scoreAnimationDuration: 2
  starAnimationDelay: 1
  enableDebugLogs: 0
  enableSaveIntegration: 1
  goldRewardPerStar: 20
  drumSound: {fileID: 8300000, guid: f2f31af0a5b121f47b56be4086da3b63, type: 3}
  enableAudio: 1
  drumVolume: 0.8
  winParticleGuid: 
  enableParticles: 0
--- !u!4 &696858327
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696858325}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &696858328
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696858325}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &797254383
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 797254385}
  - component: {fileID: 797254384}
  m_Layer: 0
  m_Name: BonusGemManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &797254384
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 797254383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f02292e7b4fc64e47af4411e0548385d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::BonusGemManager
  enableBonusGems: 1
  bonusPlacementDelay: 0.3
  bonusTriggerDelay: 0.5
  availableBonusTypes: 00000000010000000200000003000000
  bonusGemBaseScore: 60
  bonusScoreMultiplier: 1.3
  enableDebugLogs: 0
--- !u!4 &797254385
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 797254383}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &804261069
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 804261073}
  - component: {fileID: 804261072}
  - component: {fileID: 804261070}
  m_Layer: 0
  m_Name: ShowcaseCameraEffects
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &804261070
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 804261069}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.RenderPipelines.Universal.Runtime::UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
  m_RenderShadows: 0
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: 0
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 0
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
  m_Version: 2
--- !u!20 &804261072
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 804261069}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 1, g: 1, b: 1, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 1
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.2
  far clip plane: 100
  field of view: 33
  orthographic: 1
  orthographic size: 5.32
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 128
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 8400000, guid: 45a61558b6ae1b0459240fb8215d648f, type: 2}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 0
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &804261073
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 804261069}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -15, y: 0, z: 10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 412236354}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &832354369
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 832354371}
  - component: {fileID: 832354370}
  m_Layer: 0
  m_Name: ShopManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &832354370
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832354369}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4e6a90584f021a4790cdd4c2039559f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::OwnMatch3.Shop.ShopManager
  shopUIDocument: {fileID: 2063233940}
  boosterItemTemplate: {fileID: 9197481963319205126, guid: da17a49bb16c0d0409cd8b5cfde999c9, type: 3}
  avatarItemTemplate: {fileID: 9197481963319205126, guid: 40a3d0225500320438ff7a6628c79fba, type: 3}
  shopConfig: {fileID: 11400000, guid: cbd54229cb918be49a0d323f6aa59792, type: 2}
--- !u!4 &832354371
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832354369}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.19329, y: 1.46247, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &848078177
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 848078179}
  - component: {fileID: 848078178}
  - component: {fileID: 848078181}
  - component: {fileID: 848078182}
  m_Layer: 3
  m_Name: ComboManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &848078178
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848078177}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bf29aef2062c2cf439ad955d04901aaf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::ComboManager
  comboStyles:
  - word: Great
    color1: {r: 1, g: 0.85908043, b: 0.1273585, a: 1}
    color2: {r: 1, g: 0.8588236, b: 0.1254902, a: 1}
  - word: Awesome
    color1: {r: 1, g: 0.507825, b: 0.12549019, a: 1}
    color2: {r: 1, g: 0.5058824, b: 0.1254902, a: 1}
  - word: Fantastic
    color1: {r: 0.3044988, g: 1, b: 0.28773582, a: 1}
    color2: {r: 0.30588236, g: 1, b: 0.28627452, a: 1}
  - word: Super
    color1: {r: 0.28627455, g: 0.93528473, b: 1, a: 1}
    color2: {r: 0.28627452, g: 0.9333334, b: 1, a: 1}
  - word: Unstoppable
    color1: {r: 0.7083507, g: 0.5613208, b: 1, a: 1}
    color2: {r: 0.70980394, g: 0.56078434, b: 1, a: 1}
--- !u!4 &848078179
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848078177}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &848078181
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848078177}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: afcb5911e071af846aa26a4fcc5922e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::UIManager
  pointsPopupAsset: {fileID: 9197481963319205126, guid: b7d543b7f795dae44867d7e426eb9867, type: 3}
  pandaSprites:
  - {fileID: 21300000, guid: 71c678bd944796b458a42bd2b77b877d, type: 3}
  - {fileID: 21300000, guid: 3d971752301547d4e89346dbc0242193, type: 3}
--- !u!114 &848078182
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848078177}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 19102, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: UnityEngine.dll::UnityEngine.UIElements.UIDocument
  m_PanelSettings: {fileID: 11400000, guid: 8a415f75cb57ea54eb5e880422067d93, type: 2}
  m_ParentUI: {fileID: 0}
  sourceAsset: {fileID: 9197481963319205126, guid: ce7f7924b9eebbf4d997728c11cac75b, type: 3}
  m_SortingOrder: 2
  m_Position: 0
  m_WorldSpaceSizeMode: 1
  m_WorldSpaceWidth: 1920
  m_WorldSpaceHeight: 1080
  m_PivotReferenceSize: 0
  m_Pivot: 0
  m_WorldSpaceCollider: {fileID: 0}
--- !u!1 &855779045
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 855779048}
  - component: {fileID: 855779047}
  - component: {fileID: 855779046}
  m_Layer: 0
  m_Name: EmailManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &855779046
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 855779045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 87535a1b9c5745c4e8f94edbea5a86cc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::OwnMatch3.UI.EmailUIController
  emailUIDocument: {fileID: 2063233940}
  emailItemTemplate: {fileID: 9197481963319205126, guid: aff659ced39202846b554dfa3abfdb6a, type: 3}
  panelAnimationDuration: 0.4
  modalAnimationDuration: 0.3
--- !u!114 &855779047
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 855779045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 46508a1d10efdcb45a48e30ccc01648a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::OwnMatch3.UI.EmailManager
  enableEmails: 1
  maxEmailsInInbox: 50
  emailExpirationDays: 30
  dailyGiftGold: 50
  weeklyGiftGold: 150
  updateGiftGold: 200
--- !u!4 &855779048
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 855779045}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -23.75594, y: -0.87333, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &975591965
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 975591966}
  - component: {fileID: 975591969}
  - component: {fileID: 975591968}
  - component: {fileID: 975591967}
  m_Layer: 0
  m_Name: BoardFrame
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &975591966
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 975591965}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &975591967
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 975591965}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ab5b95567f2f0334b91aa7affde2c970, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::FrameBuilder
  board: {fileID: 1708267985}
  generateHoleFrames: 1
  outerSpriteShape: {fileID: 11400000, guid: b191fbc0b67c4354c94c9c24f6254a40, type: 2}
  holeSpriteShape: {fileID: 11400000, guid: 9a4a36f798ef88748b278c00b038defe, type: 2}
  subdivisions: 10
  adjustLine: 0.1
--- !u!114 &975591968
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 975591965}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.2D.SpriteShape.Runtime::UnityEngine.U2D.SpriteShapeController
  m_Spline:
    m_IsOpenEnded: 0
    m_ControlPoints:
    - position: {x: -1, y: -1, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      spriteIndex: 0
      corner: 0
      m_CornerMode: 1
    - position: {x: -1, y: 1, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      spriteIndex: 0
      corner: 0
      m_CornerMode: 1
    - position: {x: 1, y: 1, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      spriteIndex: 0
      corner: 0
      m_CornerMode: 1
    - position: {x: 1, y: -1, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      spriteIndex: 0
      corner: 0
      m_CornerMode: 1
  m_SpriteShape: {fileID: 11400000, guid: b191fbc0b67c4354c94c9c24f6254a40, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 146.34
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_CornerAngleThreshold: 90
  m_ColliderDetail: 16
  m_ColliderOffset: 0
  m_UpdateCollider: 1
  m_EnableTangents: 1
  m_GeometryCached: 0
  m_UTess2D: 1
  m_UpdateShadow: 0
  m_ShadowDetail: 16
  m_ShadowOffset: 0.5
  m_BoundsScale: 2
  m_UpdateGeometry: 1
  m_Creator: {fileID: 0}
  m_Modifiers: []
  m_ColliderSegment: []
  m_ShadowSegment: []
--- !u!1971053207 &975591969
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 975591965}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: -1
  m_Color: {r: 0, g: 0.13725491, b: 0.34509805, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  - {fileID: 21300000, guid: b281b91a70a624a0da1c43adc1c30c7b, type: 3}
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 1.5000001, y: 1.5000001, z: 0}
  m_SpriteSortPoint: 0
--- !u!1 &1066333904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1066333905}
  m_Layer: 0
  m_Name: point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1066333905
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1066333904}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.105, z: 10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1201193892}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1095218503
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1095218506}
  - component: {fileID: 1095218505}
  - component: {fileID: 1095218504}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1095218504
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1095218503}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01614664b831546d2ae94a42149d80ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.InputSystem::UnityEngine.InputSystem.UI.InputSystemUIInputModule
  m_SendPointerHoverToParent: 1
  m_MoveRepeatDelay: 0.5
  m_MoveRepeatRate: 0.1
  m_XRTrackingOrigin: {fileID: 0}
  m_ActionsAsset: {fileID: -944628639613478452, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_PointAction: {fileID: -1654692200621890270, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MoveAction: {fileID: -8784545083839296357, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_SubmitAction: {fileID: 392368643174621059, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_CancelAction: {fileID: 7727032971491509709, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_LeftClickAction: {fileID: 3001919216989983466, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MiddleClickAction: {fileID: -2185481485913320682, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_RightClickAction: {fileID: -4090225696740746782, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_ScrollWheelAction: {fileID: 6240969308177333660, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDevicePositionAction: {fileID: 6564999863303420839, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDeviceOrientationAction: {fileID: 7970375526676320489, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_DeselectOnBackgroundClick: 1
  m_PointerBehavior: 0
  m_CursorLockBehavior: 0
  m_ScrollDeltaPerTick: 6
--- !u!114 &1095218505
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1095218503}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: UnityEngine.UI::UnityEngine.EventSystems.EventSystem
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1095218506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1095218503}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1133595218
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1133595221}
  - component: {fileID: 1133595219}
  - component: {fileID: 1133595220}
  m_Layer: 0
  m_Name: Tilemap
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1839735485 &1133595219
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1133595218}
  m_Enabled: 1
  m_Tiles: {}
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  m_TileSpriteArray:
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  m_TileMatrixArray:
  - m_RefCount: 0
    m_Data:
      e00: 0.00000004813006
      e01: 0
      e02: 0.000000048130403
      e03: 1.8295932e-19
      e10: 1.3e-44
      e11: 0
      e12: 1.3e-44
      e13: 5.5e-43
      e20: 2.0631806e+34
      e21: 0
      e22: 1.6131755e+29
      e23: -3.458556e-28
      e30: 4.5907e-41
      e31: 0
      e32: 5.52e-43
      e33: 5.5e-43
  m_TileColorArray:
  - m_RefCount: 0
    m_Data: {r: 1e-45, g: 1e-45, b: 1e-45, a: 1e-45}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Origin: {x: -7, y: -5, z: 0}
  m_Size: {x: 19, y: 12, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!483693784 &1133595220
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1133595218}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4b68b1c62d585f94ba12f4bd5f6d74c3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 1890678711
  m_SortingLayer: 1
  m_SortingOrder: 1
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 0
  m_Mode: 2
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!4 &1133595221
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1133595218}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1708267986}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1201193889
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1201193892}
  - component: {fileID: 1201193891}
  - component: {fileID: 1201193890}
  - component: {fileID: 1201193893}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1201193890
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1201193889}
  m_Enabled: 1
--- !u!20 &1201193891
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1201193889}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 2
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 127
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1201193892
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1201193889}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.52000004, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1066333905}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1201193893
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1201193889}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.RenderPipelines.Universal.Runtime::UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: 0
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
  m_Version: 2
--- !u!1 &1427688304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1427688307}
  - component: {fileID: 1427688306}
  - component: {fileID: 1427688305}
  - component: {fileID: 1427688308}
  - component: {fileID: 1427688309}
  m_Layer: 5
  m_Name: GameUI Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1427688305
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1427688304}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 961a076c6962d7f429d3b62a82fe3fb6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::GameUIDocument
  board: {fileID: 1708267985}
  checkmarkIconTexture: {fileID: 2800000, guid: b3e8a413594507f4c8cb2c3fc3b7f0a0, type: 3}
  goalFont: {fileID: 12800000, guid: 544fa2be512241d4cbed24efc770654b, type: 3}
  goalFontSize: 35
  showRemainingAmount: 1
--- !u!114 &1427688306
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1427688304}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 19102, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: UnityEngine.dll::UnityEngine.UIElements.UIDocument
  m_PanelSettings: {fileID: 11400000, guid: 8a415f75cb57ea54eb5e880422067d93, type: 2}
  m_ParentUI: {fileID: 0}
  sourceAsset: {fileID: 9197481963319205126, guid: cd9b241b234765644947bc794b2caff0, type: 3}
  m_SortingOrder: 0
  m_Position: 0
  m_WorldSpaceSizeMode: 1
  m_WorldSpaceWidth: 1920
  m_WorldSpaceHeight: 1080
  m_PivotReferenceSize: 0
  m_Pivot: 0
  m_WorldSpaceCollider: {fileID: 0}
--- !u!4 &1427688307
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1427688304}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1427688308
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1427688304}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a9d382d002230214399428b480955c24, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::FPSCounter
  updateInterval: 0.5
  fpsLabelName: fps-label
  uiDocument: {fileID: 1427688306}
--- !u!114 &1427688309
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1427688304}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1a923e37e0c20fa45bef21240fcbed99, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::OwnMatch3.UI.Particles.SimpleParticleController
  uiDocument: {fileID: 1427688306}
--- !u!1 &1487110664
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1487110668}
  - component: {fileID: 1487110667}
  - component: {fileID: 1487110669}
  m_Layer: 0
  m_Name: SoundManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1487110667
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487110664}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7550709d82d402846b16f52b9c268251, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::ObstacleSoundSystem
  obstacleSounds:
  - obstacleType: 3
    destroySound: {fileID: 8300000, guid: 2b65bb5a1a164ef41923961e66fb5c3d, type: 3}
    hitSound: {fileID: 8300000, guid: c37838e23935a314cb09e1f83c215b64, type: 3}
    spawnSound: {fileID: 8300000, guid: 7592e530d2701f541b40806b974baab8, type: 3}
    volume: 0.6
    pitch: 1
  match3Board: {fileID: 1708267985}
  maxConcurrentSounds: 8
  soundCooldown: 0.1
  sfxMixerGroup: {fileID: 9013257555645461232, guid: 6073c46e496651d4484335f4d91fc61c, type: 2}
--- !u!4 &1487110668
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487110664}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &1487110669
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487110664}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 6051035814002763456, guid: 6073c46e496651d4484335f4d91fc61c, type: 2}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 1
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &1525355880
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1525355883}
  - component: {fileID: 1525355882}
  - component: {fileID: 1525355881}
  m_Layer: 0
  m_Name: ShowPandaMonk
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1525355881
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525355880}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.RenderPipelines.Universal.Runtime::UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
  m_RenderShadows: 0
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: 0
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 0
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
  m_Version: 2
--- !u!20 &1525355882
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525355880}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 1, g: 1, b: 1, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 1
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.1
  far clip plane: 100
  field of view: 33
  orthographic: 1
  orthographic size: 5.36
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 512
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 8400000, guid: 63d162bea7293404f917a128974f1f6d, type: 2}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 0
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1525355883
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525355880}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -32.42, y: 0, z: -5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5467788321408865160}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1541884270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1541884272}
  - component: {fileID: 1541884271}
  m_Layer: 0
  m_Name: __GAME_SHIELD__
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1541884271
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1541884270}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f05d3eb23171492a86a7f158566152f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: GameShield::DevsDaddy.GameShield.Core.GameShield
--- !u!4 &1541884272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1541884270}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1668655171
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1668655173}
  - component: {fileID: 1668655174}
  - component: {fileID: 1668655175}
  m_Layer: 0
  m_Name: Log
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1668655173
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1668655171}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.72151, y: 3.71322, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1668655174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1668655171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6471b3820f5d45045b7bf82f7b77f78e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::OwnMatch3.Utils.PerformanceCacheSetup
  gameUIDocument: {fileID: 1427688305}
  movesWarningAnimation: {fileID: 1708267990}
  gameUIObsolete: 1
  initializeOnAwake: 1
  assignReferencesOnStart: 0
  enableLogging: 0
  allReferencesAssigned: 1
--- !u!114 &1668655175
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1668655171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 98332acde6b55e34fbd21a565eb795cb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::LogToFile
--- !u!1 &1708267984
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1708267986}
  - component: {fileID: 1708267987}
  - component: {fileID: 1708267985}
  - component: {fileID: 1708267988}
  - component: {fileID: 1708267989}
  - component: {fileID: 1708267990}
  - component: {fileID: 1708267991}
  m_Layer: 0
  m_Name: Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1708267985
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708267984}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 283ecda25cf9c6141b60f7eabddb25dc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::Match3Board
  GemsTilemap: {fileID: 1133595219}
  gemPlacer: {fileID: 11400000, guid: 6d5d65992dec54e45acf92bfb2c08916, type: 2}
  gemTypes:
  - {fileID: 2258589283430011241, guid: c1ffcb46099919c4289d0a4832438acf, type: 3}
  - {fileID: 2258589283430011241, guid: 96df132a358671b48b17259dd1f0e636, type: 3}
  - {fileID: 2258589283430011241, guid: 41166cced6647c54199867ef84ba4ebd, type: 3}
  - {fileID: 2258589283430011241, guid: 0e320a268ac11f542b0be8dc722694bf, type: 3}
  - {fileID: 2258589283430011241, guid: b0ef7515376d5be48913d192b2b00ac5, type: 3}
  - {fileID: 2258589283430011241, guid: 8b381f85260075e4db80b082c30bbe22, type: 3}
  obstacleTileMappings:
  - tile: {fileID: 11400000, guid: 4be8cc4e6544b9f4fa88b8e69da56f9f, type: 2}
    prefab: {fileID: 636439513337716401, guid: bff3bf8d652e96341bb87f0bad710c0e, type: 3}
  - tile: {fileID: 11400000, guid: cfd48c40657cbd543940a0a170a995bc, type: 2}
    prefab: {fileID: 636439513337716401, guid: 45a5f23aa2b9bcc418d9524d8a3fdfed, type: 3}
  - tile: {fileID: 11400000, guid: 51e1ae6be2800524aa333a754c7f8713, type: 2}
    prefab: {fileID: 636439513337716401, guid: e767b3f45f6c6fc4d841f42341098458, type: 3}
  - tile: {fileID: 11400000, guid: 51e1ae6be2800524aa333a754c7f8713, type: 2}
    prefab: {fileID: 636439513337716401, guid: a6744b65c47618e4e87174bc106e5f33, type: 3}
  gemTileMappings:
  - tile: {fileID: 11400000, guid: 1c1677b00a3d2bf4e9c3ece2bea5186b, type: 2}
    prefab: {fileID: 2258589283430011241, guid: 96df132a358671b48b17259dd1f0e636, type: 3}
  - tile: {fileID: 11400000, guid: 31cbdc174555a3f42815518c069cc1f4, type: 2}
    prefab: {fileID: 2258589283430011241, guid: 41166cced6647c54199867ef84ba4ebd, type: 3}
  - tile: {fileID: 11400000, guid: a8d0f931d917972458fc1d30895718e2, type: 2}
    prefab: {fileID: 2258589283430011241, guid: 0e320a268ac11f542b0be8dc722694bf, type: 3}
  - tile: {fileID: 11400000, guid: 39bf015cf17ed524481ff25dd103ae33, type: 2}
    prefab: {fileID: 2258589283430011241, guid: b0ef7515376d5be48913d192b2b00ac5, type: 3}
  - tile: {fileID: 11400000, guid: 8f5fb142fb1246d449f6ceacb3cff84b, type: 2}
    prefab: {fileID: 2258589283430011241, guid: c1ffcb46099919c4289d0a4832438acf, type: 3}
  - tile: {fileID: 11400000, guid: 7802f4f96a5fd75479f50e459c37962a, type: 2}
    prefab: {fileID: 2258589283430011241, guid: 8b381f85260075e4db80b082c30bbe22, type: 3}
  <fallSpeed>k__BackingField: 7.5
  tweenDuration: 0.1
  BackgroundTilemap: {fileID: 317917305}
  BackgroundFillTile: {fileID: 11400000, guid: 8851aaf97fec31e41a1710416e688278, type: 2}
  backgroundSprite: {fileID: 21300000, guid: 4e01919b66f5da8428eb8785c18f3f76, type: 3}
  totalMoves: 20
  movesRemaining: 20
  isGameComplete: 0
  isGameOver: 0
  hintInactivityTime: 3
  showHints: 1
  isShowingHint: 0
  matchShapes:
  - CanMirror: 1
    CanRotate: 1
    Cells:
    - {x: 0, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 2, y: 0, z: 0}
    Bounds:
      x: 0
      y: 0
      width: 2
      height: 0
    Cell90Rot:
    - {x: 0, y: 2, z: 0}
    - {x: 0, y: 3, z: 0}
    - {x: 0, y: 4, z: 0}
    Cell180Rot:
    - {x: 2, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: 0, z: 0}
    Cell270Rot:
    - {x: 0, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 0, y: -2, z: 0}
    CellHMirror:
    - {x: 2, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: 0, z: 0}
    CellVMirror:
    - {x: 0, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 2, y: 0, z: 0}
  isResolvingMatches: 0
  isInitializing: 1
  needResolve: 0
  isTransitioning: 0
  lastMatchedGemTypes: 
  enableTilemapFrame: 1
  uiManager: {fileID: 848078181}
  gemAttractorUI: {fileID: 1708267989}
  bonusGems:
  - bonusName: Color
    bonusGemPrefab: {fileID: 2258589283430011241, guid: b99532f72b47eac43840e9601a72d714, type: 3}
    shapes:
    - CanMirror: 1
      CanRotate: 1
      Cells:
      - {x: 0, y: 0, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 4, z: 0}
      - {x: -1, y: 2, z: 0}
      - {x: -2, y: 2, z: 0}
      Bounds:
        x: -2
        y: 0
        width: 2
        height: 4
      Cell90Rot:
      - {x: -2, y: 4, z: 0}
      - {x: -3, y: 4, z: 0}
      - {x: -4, y: 4, z: 0}
      - {x: -5, y: 4, z: 0}
      - {x: -6, y: 4, z: 0}
      - {x: -4, y: 3, z: 0}
      - {x: -4, y: 2, z: 0}
      Cell180Rot:
      - {x: -2, y: 4, z: 0}
      - {x: -2, y: 3, z: 0}
      - {x: -2, y: 2, z: 0}
      - {x: -2, y: 1, z: 0}
      - {x: -2, y: 0, z: 0}
      - {x: -1, y: 2, z: 0}
      - {x: 0, y: 2, z: 0}
      Cell270Rot:
      - {x: 2, y: -2, z: 0}
      - {x: 3, y: -2, z: 0}
      - {x: 4, y: -2, z: 0}
      - {x: 5, y: -2, z: 0}
      - {x: 6, y: -2, z: 0}
      - {x: 4, y: -1, z: 0}
      - {x: 4, y: 0, z: 0}
      CellHMirror:
      - {x: -2, y: 0, z: 0}
      - {x: -2, y: 1, z: 0}
      - {x: -2, y: 2, z: 0}
      - {x: -2, y: 3, z: 0}
      - {x: -2, y: 4, z: 0}
      - {x: -1, y: 2, z: 0}
      - {x: 0, y: 2, z: 0}
      CellVMirror:
      - {x: 0, y: 4, z: 0}
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 0, z: 0}
      - {x: -1, y: 2, z: 0}
      - {x: -2, y: 2, z: 0}
    - CanMirror: 1
      CanRotate: 1
      Cells:
      - {x: 0, y: 0, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 4, z: 0}
      Bounds:
        x: 0
        y: 0
        width: 0
        height: 4
      Cell90Rot:
      - {x: 0, y: 0, z: 0}
      - {x: -1, y: 0, z: 0}
      - {x: -2, y: 0, z: 0}
      - {x: -3, y: 0, z: 0}
      - {x: -4, y: 0, z: 0}
      Cell180Rot:
      - {x: 0, y: 4, z: 0}
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 0, z: 0}
      Cell270Rot:
      - {x: 4, y: 0, z: 0}
      - {x: 5, y: 0, z: 0}
      - {x: 6, y: 0, z: 0}
      - {x: 7, y: 0, z: 0}
      - {x: 8, y: 0, z: 0}
      CellHMirror:
      - {x: 0, y: 0, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 4, z: 0}
      CellVMirror:
      - {x: 0, y: 4, z: 0}
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 0, z: 0}
    priority: 100
  - bonusName: Bomb
    bonusGemPrefab: {fileID: -2098556077236787267, guid: ed68586459def674c849e17f808fe5b1, type: 3}
    shapes:
    - CanMirror: 1
      CanRotate: 1
      Cells:
      - {x: 0, y: 0, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 2, y: 0, z: 0}
      Bounds:
        x: 0
        y: 0
        width: 2
        height: 2
      Cell90Rot:
      - {x: 0, y: 2, z: 0}
      - {x: -1, y: 2, z: 0}
      - {x: -2, y: 2, z: 0}
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 4, z: 0}
      Cell180Rot:
      - {x: 2, y: 2, z: 0}
      - {x: 2, y: 1, z: 0}
      - {x: 2, y: 0, z: 0}
      - {x: 1, y: 2, z: 0}
      - {x: 0, y: 2, z: 0}
      Cell270Rot:
      - {x: 2, y: 0, z: 0}
      - {x: 3, y: 0, z: 0}
      - {x: 4, y: 0, z: 0}
      - {x: 2, y: -1, z: 0}
      - {x: 2, y: -2, z: 0}
      CellHMirror:
      - {x: 2, y: 0, z: 0}
      - {x: 2, y: 1, z: 0}
      - {x: 2, y: 2, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 0, y: 0, z: 0}
      CellVMirror:
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 0, z: 0}
      - {x: 1, y: 2, z: 0}
      - {x: 2, y: 2, z: 0}
    - CanMirror: 1
      CanRotate: 1
      Cells:
      - {x: 0, y: 0, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: -1, y: 0, z: 0}
      - {x: -2, y: 0, z: 0}
      - {x: 0, y: -1, z: 0}
      - {x: 0, y: -2, z: 0}
      Bounds:
        x: -2
        y: -2
        width: 3
        height: 2
      Cell90Rot:
      - {x: -4, y: 3, z: 0}
      - {x: -4, y: 4, z: 0}
      - {x: -4, y: 2, z: 0}
      - {x: -4, y: 1, z: 0}
      - {x: -3, y: 3, z: 0}
      - {x: -2, y: 3, z: 0}
      Cell180Rot:
      - {x: -1, y: -2, z: 0}
      - {x: -2, y: -2, z: 0}
      - {x: 0, y: -2, z: 0}
      - {x: 1, y: -2, z: 0}
      - {x: -1, y: -1, z: 0}
      - {x: -1, y: 0, z: 0}
      Cell270Rot:
      - {x: 2, y: -4, z: 0}
      - {x: 2, y: -5, z: 0}
      - {x: 2, y: -3, z: 0}
      - {x: 2, y: -2, z: 0}
      - {x: 1, y: -4, z: 0}
      - {x: 0, y: -4, z: 0}
      CellHMirror:
      - {x: -1, y: 0, z: 0}
      - {x: -2, y: 0, z: 0}
      - {x: 0, y: 0, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: -1, y: -1, z: 0}
      - {x: -1, y: -2, z: 0}
      CellVMirror:
      - {x: 0, y: -2, z: 0}
      - {x: 1, y: -2, z: 0}
      - {x: -1, y: -2, z: 0}
      - {x: -2, y: -2, z: 0}
      - {x: 0, y: -1, z: 0}
      - {x: 0, y: 0, z: 0}
    priority: 50
  - bonusName: BombX
    bonusGemPrefab: {fileID: -2098556077236787267, guid: 92424a26df66e7c42bcbf3e082cbd897, type: 3}
    shapes:
    - CanMirror: 1
      CanRotate: 1
      Cells:
      - {x: 0, y: 0, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: -1, y: 0, z: 0}
      - {x: 0, y: -1, z: 0}
      - {x: 0, y: -2, z: 0}
      Bounds:
        x: -1
        y: -2
        width: 2
        height: 2
      Cell90Rot:
      - {x: -3, y: 1, z: 0}
      - {x: -3, y: 2, z: 0}
      - {x: -3, y: 0, z: 0}
      - {x: -2, y: 1, z: 0}
      - {x: -1, y: 1, z: 0}
      Cell180Rot:
      - {x: 0, y: -2, z: 0}
      - {x: -1, y: -2, z: 0}
      - {x: 1, y: -2, z: 0}
      - {x: 0, y: -1, z: 0}
      - {x: 0, y: 0, z: 0}
      Cell270Rot:
      - {x: 3, y: -3, z: 0}
      - {x: 3, y: -4, z: 0}
      - {x: 3, y: -2, z: 0}
      - {x: 2, y: -3, z: 0}
      - {x: 1, y: -3, z: 0}
      CellHMirror:
      - {x: 0, y: 0, z: 0}
      - {x: -1, y: 0, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 0, y: -1, z: 0}
      - {x: 0, y: -2, z: 0}
      CellVMirror:
      - {x: 0, y: -2, z: 0}
      - {x: 1, y: -2, z: 0}
      - {x: -1, y: -2, z: 0}
      - {x: 0, y: -1, z: 0}
      - {x: 0, y: 0, z: 0}
    priority: 50
  - bonusName: Fish
    bonusGemPrefab: {fileID: -2098556077236787267, guid: 784a2c2a0fa46ea4db9971bc15db418b, type: 3}
    shapes:
    - CanMirror: 1
      CanRotate: 1
      Cells:
      - {x: 0, y: 0, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 1, y: 1, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 2, y: 0, z: 0}
      Bounds:
        x: 0
        y: 0
        width: 2
        height: 1
      Cell90Rot:
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 3, z: 0}
      - {x: -1, y: 3, z: 0}
      - {x: -1, y: 2, z: 0}
      - {x: 0, y: 4, z: 0}
      Cell180Rot:
      - {x: 2, y: 1, z: 0}
      - {x: 1, y: 1, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 2, y: 0, z: 0}
      - {x: 0, y: 1, z: 0}
      Cell270Rot:
      - {x: 1, y: 0, z: 0}
      - {x: 1, y: -1, z: 0}
      - {x: 2, y: -1, z: 0}
      - {x: 2, y: 0, z: 0}
      - {x: 1, y: -2, z: 0}
      CellHMirror:
      - {x: 2, y: 0, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 1, y: 1, z: 0}
      - {x: 2, y: 1, z: 0}
      - {x: 0, y: 0, z: 0}
      CellVMirror:
      - {x: 0, y: 1, z: 0}
      - {x: 1, y: 1, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 0, y: 0, z: 0}
      - {x: 2, y: 1, z: 0}
    - CanMirror: 1
      CanRotate: 1
      Cells:
      - {x: 0, y: 0, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 1, y: 1, z: 0}
      - {x: 0, y: 1, z: 0}
      Bounds:
        x: 0
        y: 0
        width: 1
        height: 1
      Cell90Rot:
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: -1, y: 2, z: 0}
      - {x: -1, y: 1, z: 0}
      Cell180Rot:
      - {x: 1, y: 1, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 0, z: 0}
      - {x: 1, y: 0, z: 0}
      Cell270Rot:
      - {x: 1, y: 0, z: 0}
      - {x: 1, y: -1, z: 0}
      - {x: 2, y: -1, z: 0}
      - {x: 2, y: 0, z: 0}
      CellHMirror:
      - {x: 1, y: 0, z: 0}
      - {x: 0, y: 0, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 1, y: 1, z: 0}
      CellVMirror:
      - {x: 0, y: 1, z: 0}
      - {x: 1, y: 1, z: 0}
      - {x: 1, y: 0, z: 0}
      - {x: 0, y: 0, z: 0}
    priority: 50
  - bonusName: Rocket
    bonusGemPrefab: {fileID: -2098556077236787267, guid: 4afe0abf189403444a9bb2fe8b555fa9, type: 3}
    shapes:
    - CanMirror: 1
      CanRotate: 1
      Cells:
      - {x: 0, y: 0, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 3, z: 0}
      Bounds:
        x: 0
        y: 0
        width: 0
        height: 3
      Cell90Rot:
      - {x: 0, y: 0, z: 0}
      - {x: -1, y: 0, z: 0}
      - {x: -2, y: 0, z: 0}
      - {x: -3, y: 0, z: 0}
      Cell180Rot:
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 0, z: 0}
      Cell270Rot:
      - {x: 3, y: 0, z: 0}
      - {x: 4, y: 0, z: 0}
      - {x: 5, y: 0, z: 0}
      - {x: 6, y: 0, z: 0}
      CellHMirror:
      - {x: 0, y: 0, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 3, z: 0}
      CellVMirror:
      - {x: 0, y: 3, z: 0}
      - {x: 0, y: 2, z: 0}
      - {x: 0, y: 1, z: 0}
      - {x: 0, y: 0, z: 0}
    priority: 20
  horizontalRocketPrefab: {fileID: -2098556077236787267, guid: 4afe0abf189403444a9bb2fe8b555fa9, type: 3}
  verticalRocketPrefab: {fileID: -2098556077236787267, guid: 08825bfb44443e24d9916d121d60ca64, type: 3}
  BORDER_MARGIN: 0.1
--- !u!4 &1708267986
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708267984}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 317917303}
  - {fileID: 1133595221}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!156049354 &1708267987
Grid:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708267984}
  m_Enabled: 1
  m_CellSize: {x: 1, y: 1, z: 1}
  m_CellGap: {x: 0, y: 0, z: 0}
  m_CellLayout: 0
  m_CellSwizzle: 0
--- !u!114 &1708267988
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708267984}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f84390142235eb2488bafdbb11de7ab9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::LevelLoader
  levelIndex: 1
  filePattern: level_{0:D3}.json
  board: {fileID: 1708267985}
  frameBuilder: {fileID: 975591967}
  legacyGameUI: {fileID: 0}
--- !u!114 &1708267989
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708267984}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe2de1949fd40ce4eb15f99e723541dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::GemAttractorUI
  uiDocument: {fileID: 1427688306}
  mainCamera: {fileID: 1201193891}
  defaultGemSprite: {fileID: 0}
  gemSprites:
  - gemType: 1
    sprite: {fileID: 21300000, guid: dd4d5c5a4dc35f048aa38c198a87e230, type: 3}
  - gemType: 2
    sprite: {fileID: 21300000, guid: 259b83bc6f9c36c4586a1a3944b1cc22, type: 3}
  - gemType: 3
    sprite: {fileID: 21300000, guid: a92381a8f53bb1142b3b0e13ef4fe46b, type: 3}
  - gemType: 4
    sprite: {fileID: 21300000, guid: 6d1dda2996353074ab779cbf37f77bb3, type: 3}
  - gemType: 5
    sprite: {fileID: 21300000, guid: d3ff6f2608891634fb584b13846dad26, type: 3}
  - gemType: 6
    sprite: {fileID: 21300000, guid: 5a71d6b2b935ae2498a6ff7226a1d0c9, type: 3}
  defaultObstacleSprite: {fileID: 0}
  obstacleSprites: []
  animationDuration: 0.5
  punchScale: 1.2
  punchDuration: 0.2
  goalPunchTriggerProgress: 0.8
  gemSize: 60
  goalHitSound: {fileID: 8300000, guid: df7f440e346c6414baf8466042590647, type: 3}
  goalHitVolume: 0.5
  spawnDelay: 0.1
  sequentialSpawning: 1
--- !u!114 &1708267990
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708267984}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a19380da40e370d40ab18cff5195f2b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::MovesWarningAnimation
  uiDocument: {fileID: 1427688306}
  slideInDuration: 0.7
  slideOutDuration: 0.5
  rotationDuration: 0.3
  rotationAngle: 20
  warningThreshold: 5
  displayDuration: 1
  useQuickAnimation: 1
  quickAnimationDuration: 1
  useUnscaledTime: 1
  pandaSprite: {fileID: 21300000, guid: de1bb10546556f64195909fea1353529, type: 3}
  levelDataComponent: {fileID: 0}
--- !u!114 &1708267991
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708267984}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 80d69c354a4609a4ca44c0cfeda1f8d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::UpdateGame
--- !u!1 &1785511840
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1785511842}
  - component: {fileID: 1785511841}
  m_Layer: 0
  m_Name: SavingSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1785511841
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1785511840}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8c02beec8aec85f479fd9a992fe3054d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::GameProgressManager
  saveFileName: game_progress.json
  autoSave: 1
  autoSaveInterval: 30
  enableBackups: 0
  maxBackups: 3
  enableEncryption: 0
  encryptionKey: xZxU4AHDlO647yw3AMlTG8r4AV252lz2A22wHdOBJp8CqCaPHQp34gCcsjB7gWHZ
  encryptionType: 5
  enableCloudSave: 1
  cloudSaveFileName: Match2D_CloudSave
  autoCloudSync: 1
  cloudSyncInterval: 180
  preferCloudOnConflict: 1
--- !u!4 &1785511842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1785511840}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1834221689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1834221692}
  - component: {fileID: 1834221691}
  - component: {fileID: 1834221690}
  m_Layer: 8
  m_Name: IslandsSprites
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!95 &1834221690
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1834221689}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: a02a082f7fdc5334aa52840ec88a67cd, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!212 &1834221691
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1834221689}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: -5
  m_Sprite: {fileID: 7074504297974772778, guid: c975a1a1463857848a7b459ca3e5b5ac, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 17.525, y: 25.6}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &1834221692
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1834221689}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.05000019, y: 0, z: 5}
  m_LocalScale: {x: 0.45, y: 0.45, z: 0.45}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 175999381}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1880583447
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1880583449}
  - component: {fileID: 1880583448}
  m_Layer: 0
  m_Name: LevelGeneration
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1880583448
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1880583447}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6768910eca689274c863202db2f50c71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::AILevelGenerator
  config: {fileID: 11400000, guid: acbb6d2b91e66ee47b44078e72483bc7, type: 2}
  enableDebugLogging: 0
  useDeterministicSeeds: 1
  allowRegenerationVariety: 1
  availableObstacles:
  - {fileID: 636439513337716401, guid: bff3bf8d652e96341bb87f0bad710c0e, type: 3}
  - {fileID: 636439513337716401, guid: 45a5f23aa2b9bcc418d9524d8a3fdfed, type: 3}
  - {fileID: 636439513337716401, guid: e767b3f45f6c6fc4d841f42341098458, type: 3}
  - {fileID: 636439513337716401, guid: a6744b65c47618e4e87174bc106e5f33, type: 3}
  availableGems:
  - {fileID: 2258589283430011241, guid: 96df132a358671b48b17259dd1f0e636, type: 3}
  - {fileID: 2258589283430011241, guid: 41166cced6647c54199867ef84ba4ebd, type: 3}
  - {fileID: 2258589283430011241, guid: 0e320a268ac11f542b0be8dc722694bf, type: 3}
  - {fileID: 2258589283430011241, guid: b0ef7515376d5be48913d192b2b00ac5, type: 3}
  - {fileID: 2258589283430011241, guid: c1ffcb46099919c4289d0a4832438acf, type: 3}
  - {fileID: 2258589283430011241, guid: 8b381f85260075e4db80b082c30bbe22, type: 3}
--- !u!4 &1880583449
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1880583447}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.27337, y: 2.84116, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1991711876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1991711879}
  - component: {fileID: 1991711878}
  - component: {fileID: 1991711877}
  m_Layer: 0
  m_Name: GemSoundsManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1991711877
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1991711876}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b38fb70dba3c1ed4eae95c06b491ae32, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::GemSoundSystem
  matchSound: {fileID: 8300000, guid: 1e62daf203c916b4aa4fe96e67c3621f, type: 3}
--- !u!82 &1991711878
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1991711876}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 9013257555645461232, guid: 6073c46e496651d4484335f4d91fc61c, type: 2}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 3
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!4 &1991711879
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1991711876}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2063233937
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2063233941}
  - component: {fileID: 2063233940}
  - component: {fileID: 2063233939}
  - component: {fileID: 2063233943}
  - component: {fileID: 2063233942}
  - component: {fileID: 2063233944}
  - component: {fileID: 2063233945}
  - component: {fileID: 2063233946}
  m_Layer: 0
  m_Name: MainMenu
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2063233939
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063233937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 619cd490c72a3aa468985ce94699b8f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::OwnMatch3.UI.MenuUIController
  menuUIDocument: {fileID: 2063233940}
  rewardItemTemplate: {fileID: 9197481963319205126, guid: 121cb67493dbe6641b428b0446350448, type: 3}
  itemLevelTemplateAsset: {fileID: 9197481963319205126, guid: 0a3f06dcff4bcdb4d99b5c805425d2c5, type: 3}
  itemLevelAsset: {fileID: 9197481963319205126, guid: af265ab7a9b4ce548b63410908a06b67, type: 3}
  unlockedLevelSprite: {fileID: -1462993164, guid: 156ec492e516ccb4099add479601d65e, type: 3}
  lockedLevelSprite: {fileID: -834331401, guid: 156ec492e516ccb4099add479601d65e, type: 3}
  currencyAttractor: {fileID: 2063233943}
  currencyAnimationManager: {fileID: 2063233942}
  missionsUIController: {fileID: 420747023}
  emailUIController: {fileID: 855779046}
  shopManager: {fileID: 832354370}
  preGamePopupController: {fileID: 2063233946}
  enableDailyRewards: 0
--- !u!114 &2063233940
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063233937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 19102, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: UnityEngine.dll::UnityEngine.UIElements.UIDocument
  m_PanelSettings: {fileID: 11400000, guid: 8a415f75cb57ea54eb5e880422067d93, type: 2}
  m_ParentUI: {fileID: 0}
  sourceAsset: {fileID: 9197481963319205126, guid: c9521aa6c2141f442a548d6e1eeb5526, type: 3}
  m_SortingOrder: 15
  m_Position: 0
  m_WorldSpaceSizeMode: 1
  m_WorldSpaceWidth: 1920
  m_WorldSpaceHeight: 1080
  m_PivotReferenceSize: 0
  m_Pivot: 0
  m_WorldSpaceCollider: {fileID: 0}
--- !u!4 &2063233941
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063233937}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.69369, y: 0.51357, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2063233942
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063233937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f71465efbce7c2c41bd71722e81d4742, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::CurrencyAnimationManager
  animationDuration: 1
  spendingAnimationDuration: 0.8
  uiDocument: {fileID: 2063233940}
  coinsDisplayElementName: CollectedCoinsLabel
  starsDisplayElementName: CollectedStarsLabel
  coinGainSound: {fileID: 0}
  coinSpendSound: {fileID: 0}
  starGainSound: {fileID: 0}
  starSpendSound: {fileID: 0}
  enableDebugLogs: 0
--- !u!114 &2063233943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063233937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5cf8eea2292cb07448175956566e88e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::CurrencyAttractorUI
  uiDocument: {fileID: 2063233940}
  mainCamera: {fileID: 1201193891}
  coinSprite: {fileID: -1136330797, guid: a5e645d0b2507ac42807ef8e886f7d45, type: 3}
  starSprite: {fileID: 21300000, guid: 8cb4df65c6f4a2e499814646147fbc0a, type: 3}
  animationDuration: 1.5
  currencySize: 40
  goalPunchTriggerProgress: 0.8
  spawnDelay: 0.1
  sequentialSpawning: 1
  coinsTargetElementName: goldIconUp
  starsTargetElementName: starIconUp
  coinCollectSound: {fileID: 8300000, guid: 651835b9a2fdcb74b832232d2f86fdb2, type: 3}
  starCollectSound: {fileID: 8300000, guid: df7f440e346c6414baf8466042590647, type: 3}
  enableDebugLogs: 0
--- !u!114 &2063233944
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063233937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 033e4a8eae579da48bf3ea932b34be5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::CurrencyTestController
  enableTesting: 1
  coinsPerTest: 10
  starsPerTest: 1
  bigCoinAmount: 250
  bigStarAmount: 15
  useRandomWorldPositions: 1
  testWorldPosition: {x: 0, y: 0, z: 0}
  randomPositionRange: 5
  testCamera: {fileID: 1201193891}
  menuUIController: {fileID: 2063233939}
  showDebugLogs: 1
--- !u!114 &2063233945
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063233937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 512c2e285c8b304488082dfb88232087, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::GetTimeNetInitializer
--- !u!114 &2063233946
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063233937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32a2c1d622c5dc1488ff80aad09f0747, type: 3}
  m_Name: 
  m_EditorClassIdentifier: OwnMatch3::Match3.PreGamePopupController
  uiDocument: {fileID: 2063233940}
  scoreIcon: {fileID: -1123514087, guid: a5e645d0b2507ac42807ef8e886f7d45, type: 3}
--- !u!95 &312920767031444512
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780081330596890251}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 22431d13fcb8eee4b8dbe547c1f4e341, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &683926033728429608
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8948227781629536922}
  - component: {fileID: 9035215359442917943}
  m_Layer: 0
  m_Name: GameInstaller
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1780081330596890251
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5467788321408865160}
  - component: {fileID: 4182690103396394462}
  - component: {fileID: 312920767031444512}
  m_Layer: 9
  m_Name: PandaAnimation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!212 &4182690103396394462
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780081330596890251}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 2cd041682945c37429e0aab5ffce9d6f, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 5.12, y: 5.12}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &5467788321408865160
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780081330596890251}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1525355883}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &8948227781629536922
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683926033728429608}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9035215359442917943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683926033728429608}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d5707326850f719489ac067b54f355a6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 1541884272}
  - {fileID: 8948227781629536922}
  - {fileID: 1201193892}
  - {fileID: 804261073}
  - {fileID: 455206254}
  - {fileID: 175999381}
  - {fileID: 1525355883}
  - {fileID: 1708267986}
  - {fileID: 1487110668}
  - {fileID: 1991711879}
  - {fileID: 975591966}
  - {fileID: 1095218506}
  - {fileID: 1427688307}
  - {fileID: 354792331}
  - {fileID: 848078179}
  - {fileID: 1668655173}
  - {fileID: 1880583449}
  - {fileID: 404524199}
  - {fileID: 553775155}
  - {fileID: 797254385}
  - {fileID: 567975087}
  - {fileID: 696858327}
  - {fileID: 1785511842}
  - {fileID: 574392698}
  - {fileID: 2063233941}
  - {fileID: 420747022}
  - {fileID: 855779048}
  - {fileID: 464329153}
  - {fileID: 322167576}
  - {fileID: 514902275}
  - {fileID: 832354371}
