%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 53
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f2009558327c478886451e9bd1d60f8b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.AI.Material::Unity.AI.Material.Services.SessionPersistence.MaterialGeneratorSettings
  m_Session:
    settings:
      lastSelectedModels:
        serializedData:
        - key: 0
          value:
            rid: 6297302750970774182
        - key: 1
          value:
            rid: 6297302750970774183
        - key: 2
          value:
            rid: 6297302750970774184
      lastMaterialMappings:
        serializedData:
        - key: Universal Render Pipeline/Lit
          value:
            rid: 4722003967151440197
      previewSettings:
        sizeFactor: 1
  references:
    version: 2
    RefIds:
    - rid: 4722003967151440197
      type: {class: 'SerializableDictionary`2[[Unity.AI.Material.Services.Stores.States.MapType, Unity.AI.Material],[System.String, mscorlib]]', ns: Unity.AI.Generators.Redux.Toolkit, asm: Unity.AI.Generators.Redux}
      data:
        serializedData:
        - key: 1
          value: _ParallaxMap
        - key: 2
          value: _BumpMap
        - key: 3
          value: _EmissionMap
        - key: 4
          value: None
        - key: 5
          value: None
        - key: 6
          value: _BaseMap
        - key: 7
          value: _OcclusionMap
        - key: 8
          value: None
        - key: 9
          value: None
        - key: 10
          value: _MetallicGlossMap
        - key: 11
          value: None
    - rid: 6297302750970774182
      type: {class: ModelSelection, ns: Unity.AI.Material.Services.Stores.States, asm: Unity.AI.Material}
      data:
        modelID: 575ecf67-4425-4939-97b9-5e138b8ee234
    - rid: 6297302750970774183
      type: {class: ModelSelection, ns: Unity.AI.Material.Services.Stores.States, asm: Unity.AI.Material}
      data:
        modelID: 
    - rid: 6297302750970774184
      type: {class: ModelSelection, ns: Unity.AI.Material.Services.Stores.States, asm: Unity.AI.Material}
      data:
        modelID: 575ecf67-4425-4939-97b9-5e138b8ee234
