Main Thread ID: 1
CommandInvokationFailure: Unity Remote requirements check failed
F:\Unity Installs\6000.2.0b9\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools\adb.exe forward tcp:7201 tcp:7201

Environment Variables:
USERDOMAIN = DESKTOP-8GO5TD1
ProgramFiles = C:\Program Files
TMP = C:\Users\<USER>\AppData\Local\Temp
OneDrive = C:\Users\<USER>\OneDrive
PROCESSOR_ARCHITECTURE = AMD64
PROCESSOR_REVISION = 9705
OS = Windows_NT
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_13464_KLPQTZDXBPLLTCFW
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 151 Stepping 5, GenuineIntel
ProgramW6432 = C:\Program Files
EFC_8288_1592913036 = 1
USERPROFILE = C:\Users\<USER>\Users\Phantom\AppData\Local\Programs\Eclipse Adoptium\jdk-*********-hotspot
JD2_HOME = D:\Users\phant\Desktop\VideoDown\JDownloader
DriverData = C:\Windows\System32\Drivers\DriverData
ComSpec = C:\WINDOWS\system32\cmd.exe
PSModulePath = C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
PUBLIC = C:\Users\<USER>\Users\Phantom\AppData\Local\Temp
Path = C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Git\cmd;E:\Server\;E:\Python3.1\Scripts\;E:\Python3.1\;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Users\phant\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\cursor\resources\app\bin;D:\Microsoft VS Code\bin
COMPUTERNAME = DESKTOP-8GO5TD1
JAVA = C:\Program Files\Unity\Hub\Editor\6000.0.20f1\Editor\Data\PlaybackEngines\AndroidPlayer\OpenJDK\bin
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
ALLUSERSPROFILE = C:\ProgramData
SystemDrive = C:
windir = C:\WINDOWS
HOMEPATH = \Users\Phantom
CommonProgramFiles = C:\Program Files\Common Files
CommonProgramFiles(x86) = C:\Program Files (x86)\Common Files
EFC_8288_2283032206 = 1
EFC_8288_3789132940 = 1
PROCESSOR_LEVEL = 6
CommonProgramW6432 = C:\Program Files\Common Files
ProgramFiles(x86) = C:\Program Files (x86)
SystemRoot = C:\WINDOWS
SESSIONNAME = Console
LOGONSERVER = \\DESKTOP-8GO5TD1
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
APPDATA = C:\Users\<USER>\AppData\Roaming
HOMEDRIVE = C:
USERDOMAIN_ROAMINGPROFILE = DESKTOP-8GO5TD1
ProgramData = C:\ProgramData
ORIGINAL_XDG_CURRENT_DESKTOP = undefined


stderr[
adb.exe: error: no devices/emulators found
]
stdout[

]
exit code: 1
<color=orange><b>GameShield:</b></color> Injection Detection does not work in editor (check readme for details).
Tween's 'endValue' equals to the current animated value: (0.00, 0.00, 0.00, 0.00), tween: VisualElement / VisualElementOpacity / duration 0.2 / id 2 / sequence 1.
To disable this warning, set 'PrimeTweenConfig.warnEndValueEqualsCurrent = false;'.

Tween (id 2) creation stack trace:
PrimeTween.Tween:VisualElementOpacity (UnityEngine.UIElements.VisualElement,single,single,PrimeTween.Ease,int,PrimeTween.CycleMode,single,single,bool) (at ./Library/PackageCache/com.kyrylokuzyk.primetween@ab1c59930699/Runtime/Internal/TweenGenerated.cs:1775)
Match3.PreGamePopupController:HidePopupWithAnimation () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:191)
Match3.PreGamePopupController:HidePopup () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:283)
Match3.PreGamePopupController:InitializeUI () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:104)
Match3.PreGamePopupController:Awake () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:51)

Tween's 'endValue' equals to the current animated value: (0.00, 0.00, 0.00, 0.00), tween: VisualElement / VisualElementBackgroundColor / duration 0.2 / id 4 / sequence 1.
To disable this warning, set 'PrimeTweenConfig.warnEndValueEqualsCurrent = false;'.

Tween (id 4) creation stack trace:
PrimeTween.Tween:VisualElementBackgroundColor (UnityEngine.UIElements.VisualElement,UnityEngine.Color,single,PrimeTween.Ease,int,PrimeTween.CycleMode,single,single,bool) (at ./Library/PackageCache/com.kyrylokuzyk.primetween@ab1c59930699/Runtime/Internal/TweenGenerated.cs:1757)
Match3.PreGamePopupController:HidePopupWithAnimation () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:202)
Match3.PreGamePopupController:HidePopup () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:283)
Match3.PreGamePopupController:InitializeUI () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:104)
Match3.PreGamePopupController:Awake () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:51)

Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Manifest aliases:
name: External Dependency Manager --> aliases: [External Dependency Manager]
name: play-services-resolver --> aliases: [External Dependency Manager]
name: GooglePlayGamesPlugin --> aliases: []
Flattened manifest aliases:
name: External Dependency Manager --> alias: External Dependency Manager
name: play-services-resolver --> alias: External Dependency Manager
name: GooglePlayGamesPlugin --> alias: GooglePlayGamesPlugin
Add manifests to package 'External Dependency Manager':
file: Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt, version: 1.2.182
Add manifests to package 'GooglePlayGamesPlugin':
file: Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPlugin_v2.0.0.txt, version: 2.0.0
Parsing manifest 'Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt' of package 'External Dependency Manager'
'External Dependency Manager' Manifest:
Current files:
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb
Assets\ExternalDependencyManager\Editor\CHANGELOG.md
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.dll
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.pdb
Assets\ExternalDependencyManager\Editor\LICENSE
Assets\ExternalDependencyManager\Editor\README.md
Parsing manifest 'Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPlugin_v2.0.0.txt' of package 'GooglePlayGamesPlugin'
'GooglePlayGamesPlugin' Manifest:
Current files:
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb
Assets\ExternalDependencyManager\Editor\CHANGELOG.md
Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.dll
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.pdb
Assets\ExternalDependencyManager\Editor\LICENSE
Assets\ExternalDependencyManager\Editor\README.md
Assets\GooglePlayGames\AssemblyInfo.cs
Assets\GooglePlayGames\com.google.play.games\current-build\GooglePlayGamesPlugin-2.0.0.unitypackage
Assets\GooglePlayGames\com.google.play.games\Editor\Google.Play.Games.Editor.asmdef
Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPluginDependencies.xml
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSAndroidSetupUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSDocsUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSPostBuild.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSProjectSettings.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSStrings.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSUpgrader.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSUtil.cs
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\NearbyConnectionUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\template-AndroidManifest.txt
Assets\GooglePlayGames\com.google.play.games\Editor\template-Constants.txt
Assets\GooglePlayGames\com.google.play.games\Editor\template-GameInfo.txt
Assets\GooglePlayGames\com.google.play.games\package.json
Assets\GooglePlayGames\com.google.play.games\Proguard\games.txt
Assets\GooglePlayGames\com.google.play.games\Runtime\Google.Play.Games.asmdef
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Achievement.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\CommonStatusCodes.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\CommonTypes.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\DummyClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\Event.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\IEvent.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\IEventsClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\IPlayGamesClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\LeaderboardScoreData.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\AdvertisingResult.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\ConnectionRequest.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\ConnectionResponse.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\DummyNearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\EndpointDetails.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\INearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\NearbyConnectionConfiguration.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Player.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\PlayerProfile.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\PlayerStats.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\RecallAccess.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\ISavedGameClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\ISavedGameMetadata.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\SavedGameMetadataUpdate.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\ScorePageToken.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SignInInteractivity.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SignInStatus.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\GameInfo.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesAchievement.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesLeaderboard.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesLocalUser.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesPlatform.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesScore.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesUserProfile.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\Logger.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\Misc.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\NearbyHelperObject.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\PlatformUtils.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\PlayGamesHelperObject.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidEventsClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidHelperFragment.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidJavaConverter.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidNearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidSavedGameClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidSnapshotMetadata.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidTaskUtils.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\NearbyConnectionClientFactory.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\PlayGamesClientFactory.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\PluginVersion.cs
Assets\PlayServicesResolver\Editor\play-services-resolver_v1.2.137.0.txt
Assets\Plugins\Android\GooglePlayGamesManifest.androidlib\project.properties
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix EventSystem: Default constructor not found for type UnityEngine.EventSystems.EventSystem
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix EventSystem: Default constructor not found for type UnityEngine.EventSystems.EventSystem
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
No Theme Style Sheet set to PanelSettings CanvasSettings, UI will not render properly
Unknown pseudo class "last-child" in StyleSheet CameraManagerEditor
Unknown pseudo class "last-child" in StyleSheet CameraManagerEditor
Unknown pseudo class "last-child" in StyleSheet ModernAILevelGenerator
Font Asset [gomarice_rocks] Units Per EM set to 1024. Please commit the newly serialized value.
Font Asset [gomarice_rocks_purple] Units Per EM set to 1024. Please commit the newly serialized value.
Font Asset [gomarice_rocks_white_outline] Units Per EM set to 1024. Please commit the newly serialized value.
Font Asset [gomarice_rocks_yellow_outline_big] Units Per EM set to 1024. Please commit the newly serialized value.
Font Asset [gomarice_rocks_yellow_outline_small] Units Per EM set to 1024. Please commit the newly serialized value.
<color=orange><b>GameShield:</b></color> SpeedHack Detector: System DateTime change or > 1 second game freeze detected!
<color=orange><b>GameShield:</b></color> SpeedHack Detector: System DateTime change or > 1 second game freeze detected!
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🔶 REFILL Column 4: Creating 1 gems
✅ REFILL: Created gem at (4, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 1: Creating 1 gems
✅ REFILL: Created gem at (1, 8, 0)
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 1 gems need to fall
🟡 GRAVITY: Moved 1 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 3: Creating 2 gems
✅ REFILL: Created gem at (3, 2, 0)
✅ REFILL: Created gem at (3, 3, 0)
🟢 REFILL: Created 2 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 2 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 4 gems need to fall
🟡 GRAVITY: Moved 4 gems
🔵 REFILL: Found 9 empty positions total
🔶 REFILL Column 2: Creating 2 gems
✅ REFILL: Created gem at (2, 2, 0)
✅ REFILL: Created gem at (2, 3, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 1, 0)
🔶 REFILL Column 4: Creating 1 gems
✅ REFILL: Created gem at (4, 1, 0)
🟢 REFILL: Created 4 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 4 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 1 gems need to fall
🟡 GRAVITY: Moved 1 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 1: Creating 2 gems
✅ REFILL: Created gem at (1, 2, 0)
✅ REFILL: Created gem at (1, 3, 0)
🟢 REFILL: Created 2 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 2 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 3 gems need to fall
🟡 GRAVITY: Moved 3 gems
🔵 REFILL: Found 3 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
Parsed Time: 7/23/2025 11:08:14 PM, Unix Time: 1753304894 from http://www.microsoft.com
Parsed Time: 7/23/2025 11:08:14 PM, Unix Time: 1753304894 from http://www.microsoft.com
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 4 gems need to fall
🟡 GRAVITY: Moved 4 gems
🔵 REFILL: Found 4 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 3 gems need to fall
🟡 GRAVITY: Moved 3 gems
🔵 REFILL: Found 5 empty positions total
🔶 REFILL Column 5: Creating 2 gems
✅ REFILL: Created gem at (5, 2, 0)
✅ REFILL: Created gem at (5, 3, 0)
🟢 REFILL: Created 2 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 2 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 4: Creating 3 gems
✅ REFILL: Created gem at (4, 6, 0)
✅ REFILL: Created gem at (4, 7, 0)
✅ REFILL: Created gem at (4, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 3 gems need to fall
🟡 GRAVITY: Moved 3 gems
🔵 REFILL: Found 3 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 3 gems need to fall
🟡 GRAVITY: Moved 3 gems
🔵 REFILL: Found 4 empty positions total
🟢 REFILL: Created 0 new gems
Please use Tween/Sequence.ToYieldInstruction() when waiting for a Tween/Sequence in coroutines to prevent struct boxing.
To disable this warning, set 'PrimeTweenConfig.warnStructBoxingAllocationInCoroutine = false;'.

Tween (id 1590) creation stack trace:
PrimeTween.Tween:Position (UnityEngine.Transform,UnityEngine.Vector3,single,PrimeTween.Ease,int,PrimeTween.CycleMode,single,single,bool) (at ./Library/PackageCache/com.kyrylokuzyk.primetween@ab1c59930699/Runtime/Internal/TweenGenerated.cs:394)
LineRocketBonus/<AnimateRocketMovement>d__18:MoveNext () (at /OwnMatch3/Scripts/Bonuses/LineRocketBonus.cs:377)
LineRocketBonus:AnimateRocketMovement (UnityEngine.GameObject,UnityEngine.Vector3,UnityEngine.Vector3,System.Threading.CancellationToken)
LineRocketBonus/<LaunchRocketAsync>d__17:MoveNext () (at /OwnMatch3/Scripts/Bonuses/LineRocketBonus.cs:173)
LineRocketBonus:LaunchRocketAsync (UnityEngine.GameObject,UnityEngine.Vector3Int,UnityEngine.Vector3Int,Match3Board)
LineRocketBonus/<CreateRocket>d__15:MoveNext () (at /OwnMatch3/Scripts/Bonuses/LineRocketBonus.cs:98)
LineRocketBonus:CreateRocket (GemNew,UnityEngine.Vector3Int,Match3Board)
LineRocketBonus/<ActivateBonus>d__14:MoveNext () (at /OwnMatch3/Scripts/Bonuses/LineRocketBonus.cs:73)
LineRocketBonus:ActivateBonus (GemNew)
GemNew/<OnMatched>d__8:MoveNext () (at /OwnMatch3/Scripts/Gems/GemNew.cs:51)
GemNew:OnMatched (Match3Board)
Match3Board/<ClearMatchesAsync>d__174:MoveNext () (at /OwnMatch3/Scripts/Match3Match/BoardMatchResolver.cs:225)
Match3Board:ClearMatchesAsync ()
Match3Board/<TrySwap>d__157:MoveNext () (at /OwnMatch3/Scripts/Match3Logic/BoardLogic.cs:488)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2<Match3Board/<TrySwap>d__157, bool>:Run () (at /Plugins/UniTask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation (object) (at /Plugins/UniTask/Runtime/UniTask.cs:25)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1<bool>:TrySetResult (bool) (at /Plugins/UniTask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2<Match3Board/<ValidateAndFixBoardIntegrity>d__83, bool>:SetResult (bool) (at /Plugins/UniTask/Runtime/CompilerServices/StateMachineRunner.cs:328)
Match3Board/<ValidateAndFixBoardIntegrity>d__83:MoveNext () (at /OwnMatch3/Scripts/Main/Match3Board.cs:440)
Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask`2<Match3Board/<ValidateAndFixBoardIntegrity>d__83, bool>:Run () (at /Plugins/UniTask/Runtime/CompilerServices/StateMachineRunner.cs:313)
Cysharp.Threading.Tasks.AwaiterActions:Continuation (object) (at /Plugins/UniTask/Runtime/UniTask.cs:25)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1<Cysharp.Threading.Tasks.AsyncUnit>:TrySetResult (Cysharp.Threading.Tasks.AsyncUnit) (at /Plugins/UniTask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.UniTask/WhenAllPromise:TryInvokeContinuation (Cysharp.Threading.Tasks.UniTask/WhenAllPromise,Cysharp.Threading.Tasks.UniTask/Awaiter&) (at /Plugins/UniTask/Runtime/UniTask.WhenAll.cs:210)
Cysharp.Threading.Tasks.UniTask/WhenAllPromise/<>c:<.ctor>b__3_0 (object) (at /Plugins/UniTask/Runtime/UniTask.WhenAll.cs:189)
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1<object>:TrySetResult (object) (at /Plugins/UniTask/Runtime/UniTaskCompletionSource.cs:139)
Cysharp.Threading.Tasks.EnumeratorAsyncExtensions/EnumeratorPromise:MoveNext () (at /Plugins/UniTask/Runtime/EnumeratorAsyncExtensions.cs:181)
Cysharp.Threading.Tasks.Internal.PlayerLoopRunner:RunCore () (at /Plugins/UniTask/Runtime/Internal/PlayerLoopRunner.cs:203)
Cysharp.Threading.Tasks.Internal.PlayerLoopRunner:Update () (at /Plugins/UniTask/Runtime/Internal/PlayerLoopRunner.cs:145)
Cysharp.Threading.Tasks.Internal.PlayerLoopRunner:Run () (at /Plugins/UniTask/Runtime/Internal/PlayerLoopRunner.cs:104)

🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 1 gems need to fall
🟡 GRAVITY: Moved 1 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 3: Creating 3 gems
✅ REFILL: Created gem at (3, 0, 0)
✅ REFILL: Created gem at (3, 1, 0)
✅ REFILL: Created gem at (3, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 2 empty positions total
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🟢 REFILL: Created 2 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 2 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 1 empty positions total
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🟢 REFILL: Created 1 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 1 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 1 gems need to fall
🟡 GRAVITY: Moved 1 gems
🔵 REFILL: Found 4 empty positions total
🔶 REFILL Column 3: Creating 3 gems
✅ REFILL: Created gem at (3, 1, 0)
✅ REFILL: Created gem at (3, 2, 0)
✅ REFILL: Created gem at (3, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 3 gems need to fall
🟡 GRAVITY: Moved 3 gems
🔵 REFILL: Found 9 empty positions total
🔶 REFILL Column 3: Creating 2 gems
✅ REFILL: Created gem at (3, 1, 0)
✅ REFILL: Created gem at (3, 2, 0)
🔶 REFILL Column 4: Creating 2 gems
✅ REFILL: Created gem at (4, 1, 0)
✅ REFILL: Created gem at (4, 2, 0)
🔶 REFILL Column 5: Creating 2 gems
✅ REFILL: Created gem at (5, 1, 0)
✅ REFILL: Created gem at (5, 2, 0)
🟢 REFILL: Created 6 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 6 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
NetworkTimeDiff: 30, LocalTimeDiff: 30, AvgTimeDiff: 0
Parsed Time: 7/23/2025 11:08:44 PM, Unix Time: 1753304924 from http://www.microsoft.com
NetworkTimeDiff: 30, LocalTimeDiff: 30, AvgTimeDiff: 0
Parsed Time: 7/23/2025 11:08:44 PM, Unix Time: 1753304924 from http://www.microsoft.com
Main Thread ID: 1
CommandInvokationFailure: Unity Remote requirements check failed
F:\Unity Installs\6000.2.0b9\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools\adb.exe forward tcp:7201 tcp:7201

Environment Variables:
USERDOMAIN = DESKTOP-8GO5TD1
ProgramFiles = C:\Program Files
TMP = C:\Users\<USER>\AppData\Local\Temp
OneDrive = C:\Users\<USER>\OneDrive
PROCESSOR_ARCHITECTURE = AMD64
PROCESSOR_REVISION = 9705
OS = Windows_NT
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_13464_KLPQTZDXBPLLTCFW
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 151 Stepping 5, GenuineIntel
ProgramW6432 = C:\Program Files
EFC_8288_1592913036 = 1
USERPROFILE = C:\Users\<USER>\Users\Phantom\AppData\Local\Programs\Eclipse Adoptium\jdk-*********-hotspot
JD2_HOME = D:\Users\phant\Desktop\VideoDown\JDownloader
DriverData = C:\Windows\System32\Drivers\DriverData
ComSpec = C:\WINDOWS\system32\cmd.exe
PSModulePath = C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
PUBLIC = C:\Users\<USER>\Users\Phantom\AppData\Local\Temp
Path = C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Git\cmd;E:\Server\;E:\Python3.1\Scripts\;E:\Python3.1\;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Users\phant\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\cursor\resources\app\bin;D:\Microsoft VS Code\bin
COMPUTERNAME = DESKTOP-8GO5TD1
JAVA = C:\Program Files\Unity\Hub\Editor\6000.0.20f1\Editor\Data\PlaybackEngines\AndroidPlayer\OpenJDK\bin
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
ALLUSERSPROFILE = C:\ProgramData
SystemDrive = C:
windir = C:\WINDOWS
HOMEPATH = \Users\Phantom
CommonProgramFiles = C:\Program Files\Common Files
CommonProgramFiles(x86) = C:\Program Files (x86)\Common Files
EFC_8288_2283032206 = 1
EFC_8288_3789132940 = 1
PROCESSOR_LEVEL = 6
CommonProgramW6432 = C:\Program Files\Common Files
ProgramFiles(x86) = C:\Program Files (x86)
SystemRoot = C:\WINDOWS
SESSIONNAME = Console
LOGONSERVER = \\DESKTOP-8GO5TD1
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
APPDATA = C:\Users\<USER>\AppData\Roaming
HOMEDRIVE = C:
USERDOMAIN_ROAMINGPROFILE = DESKTOP-8GO5TD1
ProgramData = C:\ProgramData
ORIGINAL_XDG_CURRENT_DESKTOP = undefined


stderr[
adb.exe: error: no devices/emulators found
]
stdout[

]
exit code: 1
<color=orange><b>GameShield:</b></color> Injection Detection does not work in editor (check readme for details).
Tween's 'endValue' equals to the current animated value: (0.00, 0.00, 0.00, 0.00), tween: VisualElement / VisualElementOpacity / duration 0.2 / id 2 / sequence 1.
To disable this warning, set 'PrimeTweenConfig.warnEndValueEqualsCurrent = false;'.

Tween (id 2) creation stack trace:
PrimeTween.Tween:VisualElementOpacity (UnityEngine.UIElements.VisualElement,single,single,PrimeTween.Ease,int,PrimeTween.CycleMode,single,single,bool) (at ./Library/PackageCache/com.kyrylokuzyk.primetween@ab1c59930699/Runtime/Internal/TweenGenerated.cs:1775)
Match3.PreGamePopupController:HidePopupWithAnimation () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:191)
Match3.PreGamePopupController:HidePopup () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:283)
Match3.PreGamePopupController:InitializeUI () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:104)
Match3.PreGamePopupController:Awake () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:51)

Tween's 'endValue' equals to the current animated value: (0.00, 0.00, 0.00, 0.00), tween: VisualElement / VisualElementBackgroundColor / duration 0.2 / id 4 / sequence 1.
To disable this warning, set 'PrimeTweenConfig.warnEndValueEqualsCurrent = false;'.

Tween (id 4) creation stack trace:
PrimeTween.Tween:VisualElementBackgroundColor (UnityEngine.UIElements.VisualElement,UnityEngine.Color,single,PrimeTween.Ease,int,PrimeTween.CycleMode,single,single,bool) (at ./Library/PackageCache/com.kyrylokuzyk.primetween@ab1c59930699/Runtime/Internal/TweenGenerated.cs:1757)
Match3.PreGamePopupController:HidePopupWithAnimation () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:202)
Match3.PreGamePopupController:HidePopup () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:283)
Match3.PreGamePopupController:InitializeUI () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:104)
Match3.PreGamePopupController:Awake () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:51)

Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Manifest aliases:
name: External Dependency Manager --> aliases: [External Dependency Manager]
name: play-services-resolver --> aliases: [External Dependency Manager]
name: GooglePlayGamesPlugin --> aliases: []
Flattened manifest aliases:
name: External Dependency Manager --> alias: External Dependency Manager
name: play-services-resolver --> alias: External Dependency Manager
name: GooglePlayGamesPlugin --> alias: GooglePlayGamesPlugin
Add manifests to package 'External Dependency Manager':
file: Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt, version: 1.2.182
Add manifests to package 'GooglePlayGamesPlugin':
file: Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPlugin_v2.0.0.txt, version: 2.0.0
Parsing manifest 'Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt' of package 'External Dependency Manager'
'External Dependency Manager' Manifest:
Current files:
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb
Assets\ExternalDependencyManager\Editor\CHANGELOG.md
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.dll
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.pdb
Assets\ExternalDependencyManager\Editor\LICENSE
Assets\ExternalDependencyManager\Editor\README.md
Parsing manifest 'Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPlugin_v2.0.0.txt' of package 'GooglePlayGamesPlugin'
'GooglePlayGamesPlugin' Manifest:
Current files:
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb
Assets\ExternalDependencyManager\Editor\CHANGELOG.md
Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.dll
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.pdb
Assets\ExternalDependencyManager\Editor\LICENSE
Assets\ExternalDependencyManager\Editor\README.md
Assets\GooglePlayGames\AssemblyInfo.cs
Assets\GooglePlayGames\com.google.play.games\current-build\GooglePlayGamesPlugin-2.0.0.unitypackage
Assets\GooglePlayGames\com.google.play.games\Editor\Google.Play.Games.Editor.asmdef
Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPluginDependencies.xml
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSAndroidSetupUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSDocsUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSPostBuild.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSProjectSettings.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSStrings.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSUpgrader.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSUtil.cs
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\NearbyConnectionUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\template-AndroidManifest.txt
Assets\GooglePlayGames\com.google.play.games\Editor\template-Constants.txt
Assets\GooglePlayGames\com.google.play.games\Editor\template-GameInfo.txt
Assets\GooglePlayGames\com.google.play.games\package.json
Assets\GooglePlayGames\com.google.play.games\Proguard\games.txt
Assets\GooglePlayGames\com.google.play.games\Runtime\Google.Play.Games.asmdef
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Achievement.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\CommonStatusCodes.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\CommonTypes.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\DummyClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\Event.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\IEvent.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\IEventsClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\IPlayGamesClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\LeaderboardScoreData.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\AdvertisingResult.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\ConnectionRequest.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\ConnectionResponse.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\DummyNearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\EndpointDetails.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\INearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\NearbyConnectionConfiguration.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Player.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\PlayerProfile.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\PlayerStats.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\RecallAccess.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\ISavedGameClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\ISavedGameMetadata.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\SavedGameMetadataUpdate.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\ScorePageToken.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SignInInteractivity.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SignInStatus.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\GameInfo.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesAchievement.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesLeaderboard.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesLocalUser.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesPlatform.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesScore.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesUserProfile.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\Logger.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\Misc.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\NearbyHelperObject.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\PlatformUtils.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\PlayGamesHelperObject.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidEventsClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidHelperFragment.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidJavaConverter.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidNearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidSavedGameClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidSnapshotMetadata.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidTaskUtils.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\NearbyConnectionClientFactory.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\PlayGamesClientFactory.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\PluginVersion.cs
Assets\PlayServicesResolver\Editor\play-services-resolver_v1.2.137.0.txt
Assets\Plugins\Android\GooglePlayGamesManifest.androidlib\project.properties
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix EventSystem: Default constructor not found for type UnityEngine.EventSystems.EventSystem
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix EventSystem: Default constructor not found for type UnityEngine.EventSystems.EventSystem
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
No Theme Style Sheet set to PanelSettings CanvasSettings, UI will not render properly
Unknown pseudo class "last-child" in StyleSheet CameraManagerEditor
Unknown pseudo class "last-child" in StyleSheet CameraManagerEditor
Unknown pseudo class "last-child" in StyleSheet ModernAILevelGenerator
<color=orange><b>GameShield:</b></color> SpeedHack Detector: System DateTime change or > 1 second game freeze detected!
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🔶 REFILL Column 4: Creating 1 gems
✅ REFILL: Created gem at (4, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🔶 REFILL Column 4: Creating 1 gems
✅ REFILL: Created gem at (4, 8, 0)
🔶 REFILL Column 5: Creating 1 gems
✅ REFILL: Created gem at (5, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🔶 REFILL Column 4: Creating 1 gems
✅ REFILL: Created gem at (4, 8, 0)
🔶 REFILL Column 5: Creating 1 gems
✅ REFILL: Created gem at (5, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 1 gems need to fall
🟡 GRAVITY: Moved 1 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 1: Creating 2 gems
✅ REFILL: Created gem at (1, 3, 0)
✅ REFILL: Created gem at (1, 4, 0)
🟢 REFILL: Created 2 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 2 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 1 gems need to fall
🟡 GRAVITY: Moved 1 gems
🔵 REFILL: Found 4 empty positions total
🔶 REFILL Column 1: Creating 2 gems
✅ REFILL: Created gem at (1, 4, 0)
✅ REFILL: Created gem at (1, 5, 0)
🟢 REFILL: Created 2 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 2 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 3 empty positions total
🔶 REFILL Column 1: Creating 1 gems
✅ REFILL: Created gem at (1, 8, 0)
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 3 gems need to fall
🟡 GRAVITY: Moved 3 gems
🔵 REFILL: Found 7 empty positions total
🔶 REFILL Column 0: Creating 1 gems
✅ REFILL: Created gem at (0, 8, 0)
🔶 REFILL Column 1: Creating 2 gems
✅ REFILL: Created gem at (1, 7, 0)
✅ REFILL: Created gem at (1, 8, 0)
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🟢 REFILL: Created 4 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 4 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 3 gems need to fall
🟡 GRAVITY: Moved 3 gems
🔵 REFILL: Found 6 empty positions total
🔶 REFILL Column 1: Creating 1 gems
✅ REFILL: Created gem at (1, 8, 0)
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 4 empty positions total
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🔶 REFILL Column 4: Creating 1 gems
✅ REFILL: Created gem at (4, 8, 0)
🟢 REFILL: Created 3 new gems
🔄 APPLYING SIMPLE GRAVITY: Moving 3 new gems down
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
🔍 BOARD BOUNDS: X(0 to 6) Y(0 to 8)
🔵 GRAVITY: 0 gems need to fall
🟡 GRAVITY: Moved 0 gems
🔵 REFILL: Found 0 empty positions total
🟢 REFILL: Created 0 new gems
Parsed Time: 7/23/2025 11:12:22 PM, Unix Time: 1753305142 from http://www.microsoft.com
Parsed Time: 7/23/2025 11:12:22 PM, Unix Time: 1753305142 from http://www.microsoft.com
Main Thread ID: 1
CommandInvokationFailure: Unity Remote requirements check failed
F:\Unity Installs\6000.2.0b9\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools\adb.exe forward tcp:7201 tcp:7201

Environment Variables:
USERDOMAIN = DESKTOP-8GO5TD1
ProgramFiles = C:\Program Files
TMP = C:\Users\<USER>\AppData\Local\Temp
OneDrive = C:\Users\<USER>\OneDrive
PROCESSOR_ARCHITECTURE = AMD64
PROCESSOR_REVISION = 9705
OS = Windows_NT
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_13464_KLPQTZDXBPLLTCFW
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 151 Stepping 5, GenuineIntel
ProgramW6432 = C:\Program Files
EFC_8288_1592913036 = 1
USERPROFILE = C:\Users\<USER>\Users\Phantom\AppData\Local\Programs\Eclipse Adoptium\jdk-*********-hotspot
JD2_HOME = D:\Users\phant\Desktop\VideoDown\JDownloader
DriverData = C:\Windows\System32\Drivers\DriverData
ComSpec = C:\WINDOWS\system32\cmd.exe
PSModulePath = C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
PUBLIC = C:\Users\<USER>\Users\Phantom\AppData\Local\Temp
Path = C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Git\cmd;E:\Server\;E:\Python3.1\Scripts\;E:\Python3.1\;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Users\phant\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\cursor\resources\app\bin;D:\Microsoft VS Code\bin
COMPUTERNAME = DESKTOP-8GO5TD1
JAVA = C:\Program Files\Unity\Hub\Editor\6000.0.20f1\Editor\Data\PlaybackEngines\AndroidPlayer\OpenJDK\bin
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
ALLUSERSPROFILE = C:\ProgramData
SystemDrive = C:
windir = C:\WINDOWS
HOMEPATH = \Users\Phantom
CommonProgramFiles = C:\Program Files\Common Files
CommonProgramFiles(x86) = C:\Program Files (x86)\Common Files
EFC_8288_2283032206 = 1
EFC_8288_3789132940 = 1
PROCESSOR_LEVEL = 6
CommonProgramW6432 = C:\Program Files\Common Files
ProgramFiles(x86) = C:\Program Files (x86)
SystemRoot = C:\WINDOWS
SESSIONNAME = Console
LOGONSERVER = \\DESKTOP-8GO5TD1
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
APPDATA = C:\Users\<USER>\AppData\Roaming
HOMEDRIVE = C:
USERDOMAIN_ROAMINGPROFILE = DESKTOP-8GO5TD1
ProgramData = C:\ProgramData
ORIGINAL_XDG_CURRENT_DESKTOP = undefined


stderr[
adb.exe: error: no devices/emulators found
]
stdout[

]
exit code: 1
<color=orange><b>GameShield:</b></color> Injection Detection does not work in editor (check readme for details).
Tween's 'endValue' equals to the current animated value: (0.00, 0.00, 0.00, 0.00), tween: VisualElement / VisualElementOpacity / duration 0.2 / id 2 / sequence 1.
To disable this warning, set 'PrimeTweenConfig.warnEndValueEqualsCurrent = false;'.

Tween (id 2) creation stack trace:
PrimeTween.Tween:VisualElementOpacity (UnityEngine.UIElements.VisualElement,single,single,PrimeTween.Ease,int,PrimeTween.CycleMode,single,single,bool) (at ./Library/PackageCache/com.kyrylokuzyk.primetween@ab1c59930699/Runtime/Internal/TweenGenerated.cs:1775)
Match3.PreGamePopupController:HidePopupWithAnimation () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:191)
Match3.PreGamePopupController:HidePopup () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:283)
Match3.PreGamePopupController:InitializeUI () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:104)
Match3.PreGamePopupController:Awake () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:51)

Tween's 'endValue' equals to the current animated value: (0.00, 0.00, 0.00, 0.00), tween: VisualElement / VisualElementBackgroundColor / duration 0.2 / id 4 / sequence 1.
To disable this warning, set 'PrimeTweenConfig.warnEndValueEqualsCurrent = false;'.

Tween (id 4) creation stack trace:
PrimeTween.Tween:VisualElementBackgroundColor (UnityEngine.UIElements.VisualElement,UnityEngine.Color,single,PrimeTween.Ease,int,PrimeTween.CycleMode,single,single,bool) (at ./Library/PackageCache/com.kyrylokuzyk.primetween@ab1c59930699/Runtime/Internal/TweenGenerated.cs:1757)
Match3.PreGamePopupController:HidePopupWithAnimation () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:202)
Match3.PreGamePopupController:HidePopup () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:283)
Match3.PreGamePopupController:InitializeUI () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:104)
Match3.PreGamePopupController:Awake () (at /OwnMatch3/Scripts/UI/PreGamePopupController.cs:51)

Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb: editor enabled True, build targets [] (current target Android)
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb: editor enabled True, build targets [] (current target Android)
Manifest aliases:
name: External Dependency Manager --> aliases: [External Dependency Manager]
name: play-services-resolver --> aliases: [External Dependency Manager]
name: GooglePlayGamesPlugin --> aliases: []
Flattened manifest aliases:
name: External Dependency Manager --> alias: External Dependency Manager
name: play-services-resolver --> alias: External Dependency Manager
name: GooglePlayGamesPlugin --> alias: GooglePlayGamesPlugin
Add manifests to package 'External Dependency Manager':
file: Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt, version: 1.2.182
Add manifests to package 'GooglePlayGamesPlugin':
file: Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPlugin_v2.0.0.txt, version: 2.0.0
Parsing manifest 'Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt' of package 'External Dependency Manager'
'External Dependency Manager' Manifest:
Current files:
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb
Assets\ExternalDependencyManager\Editor\CHANGELOG.md
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.dll
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.pdb
Assets\ExternalDependencyManager\Editor\LICENSE
Assets\ExternalDependencyManager\Editor\README.md
Parsing manifest 'Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPlugin_v2.0.0.txt' of package 'GooglePlayGamesPlugin'
'GooglePlayGamesPlugin' Manifest:
Current files:
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.pdb
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.dll
Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.pdb
Assets\ExternalDependencyManager\Editor\CHANGELOG.md
Assets\ExternalDependencyManager\Editor\external-dependency-manager_version-1.2.182_manifest.txt
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.dll
Assets\ExternalDependencyManager\Editor\Google.VersionHandler.pdb
Assets\ExternalDependencyManager\Editor\LICENSE
Assets\ExternalDependencyManager\Editor\README.md
Assets\GooglePlayGames\AssemblyInfo.cs
Assets\GooglePlayGames\com.google.play.games\current-build\GooglePlayGamesPlugin-2.0.0.unitypackage
Assets\GooglePlayGames\com.google.play.games\Editor\Google.Play.Games.Editor.asmdef
Assets\GooglePlayGames\com.google.play.games\Editor\GooglePlayGamesPluginDependencies.xml
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSAndroidSetupUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSDocsUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSPostBuild.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSProjectSettings.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSStrings.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSUpgrader.cs
Assets\GooglePlayGames\com.google.play.games\Editor\GPGSUtil.cs
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.pom.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\2.0.0\gpgs-plugin-support-2.0.0.srcaar.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.md5
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha1
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha256
Assets\GooglePlayGames\com.google.play.games\Editor\m2repository\com\google\games\gpgs-plugin-support\maven-metadata.xml.sha512
Assets\GooglePlayGames\com.google.play.games\Editor\NearbyConnectionUI.cs
Assets\GooglePlayGames\com.google.play.games\Editor\template-AndroidManifest.txt
Assets\GooglePlayGames\com.google.play.games\Editor\template-Constants.txt
Assets\GooglePlayGames\com.google.play.games\Editor\template-GameInfo.txt
Assets\GooglePlayGames\com.google.play.games\package.json
Assets\GooglePlayGames\com.google.play.games\Proguard\games.txt
Assets\GooglePlayGames\com.google.play.games\Runtime\Google.Play.Games.asmdef
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Achievement.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\CommonStatusCodes.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\CommonTypes.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\DummyClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\Event.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\IEvent.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Events\IEventsClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\IPlayGamesClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\LeaderboardScoreData.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\AdvertisingResult.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\ConnectionRequest.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\ConnectionResponse.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\DummyNearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\EndpointDetails.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\INearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Nearby\NearbyConnectionConfiguration.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\Player.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\PlayerProfile.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\PlayerStats.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\RecallAccess.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\ISavedGameClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\ISavedGameMetadata.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SavedGame\SavedGameMetadataUpdate.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\ScorePageToken.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SignInInteractivity.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\BasicApi\SignInStatus.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\GameInfo.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesAchievement.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesLeaderboard.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesLocalUser.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesPlatform.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesScore.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\ISocialPlatform\PlayGamesUserProfile.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\Logger.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\Misc.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\NearbyHelperObject.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\PlatformUtils.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\OurUtils\PlayGamesHelperObject.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidEventsClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidHelperFragment.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidJavaConverter.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidNearbyConnectionClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidSavedGameClient.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidSnapshotMetadata.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\Android\AndroidTaskUtils.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\NearbyConnectionClientFactory.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\Platforms\PlayGamesClientFactory.cs
Assets\GooglePlayGames\com.google.play.games\Runtime\Scripts\PluginVersion.cs
Assets\PlayServicesResolver\Editor\play-services-resolver_v1.2.137.0.txt
Assets\Plugins\Android\GooglePlayGamesManifest.androidlib\project.properties
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix EventSystem: Default constructor not found for type UnityEngine.EventSystems.EventSystem
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix EventSystem: Default constructor not found for type UnityEngine.EventSystems.EventSystem
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
[FieldInitFix] Could not fix UIDocument: Default constructor not found for type UnityEngine.UIElements.UIDocument
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
You are trying to create a MonoBehaviour using the 'new' keyword.  This is not allowed.  MonoBehaviours can only be added using AddComponent(). Alternatively, your script can inherit from ScriptableObject or no base class at all
No Theme Style Sheet set to PanelSettings CanvasSettings, UI will not render properly
Unknown pseudo class "last-child" in StyleSheet CameraManagerEditor
Unknown pseudo class "last-child" in StyleSheet CameraManagerEditor
Unknown pseudo class "last-child" in StyleSheet ModernAILevelGenerator
<color=orange><b>GameShield:</b></color> SpeedHack Detector: System DateTime change or > 1 second game freeze detected!
🔶 REFILL Column 2: Creating 1 gems
✅ REFILL: Created gem at (2, 8, 0)
🔶 REFILL Column 3: Creating 1 gems
✅ REFILL: Created gem at (3, 8, 0)
🔶 REFILL Column 4: Creating 1 gems
✅ REFILL: Created gem at (4, 8, 0)
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
<color=orange><b>GameShield:</b></color> SpeedHack Detector: System DateTime change or > 1 second game freeze detected!
🔶 REFILL Column 1: Creating 2 gems
✅ REFILL: Created gem at (1, 3, 0)
✅ REFILL: Created gem at (1, 4, 0)
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
Tween duration (0) <= 0. To disable this warning, set 'PrimeTweenConfig.warnZeroDuration = false;'.
