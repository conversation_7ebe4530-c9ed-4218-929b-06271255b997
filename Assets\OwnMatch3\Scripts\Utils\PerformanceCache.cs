using System.Collections.Generic;
using UnityEngine;
using System.Collections.Concurrent;
using Cysharp.Threading.Tasks;

namespace OwnMatch3.Utils
{
    /// <summary>
    /// High-performance caching system to eliminate expensive FindFirstObjectByType calls
    /// and reduce GC allocations in critical game loops
    /// </summary>
    public static class PerformanceCache
    {
        // UI Component Cache (GameUI removed - obsolete, using GameUIDocument only)
        private static GameUIDocument _cachedGameUIDocument;
        private static MovesWarningAnimation _cachedMovesWarning;
        
        // Component Cache with type safety
        private static readonly Dictionary<System.Type, Component> _componentCache = new();
        
        // Reusable Collections Pool to reduce GC allocations
        private static readonly ConcurrentQueue<List<Vector3Int>> _vector3IntListPool = new();
        private static readonly ConcurrentQueue<List<GemNew>> _gemListPool = new();
        private static readonly ConcurrentQueue<HashSet<Vector3Int>> _vector3IntHashSetPool = new();
        private static readonly ConcurrentQueue<Dictionary<Vector3Int, GemNew>> _gemDictionaryPool = new();

        // ULTRA-FAST: Pre-allocated collections for critical methods (never pooled, always available)
        private static readonly List<Vector3Int> _ultraFastVector3IntList = new(128);
        private static readonly HashSet<Vector3Int> _ultraFastHashSet = new(128);
        private static readonly List<GemNew> _ultraFastGemList = new(64);
        private static readonly List<(Vector3Int pos, GemNew gem)> _ultraFastGemPositionList = new(64);
        private static readonly List<UniTask> _ultraFastUniTaskList = new(32);
        
        // Cache validation flags
        private static bool _isInitialized = false;
        
        #region Initialization
        
        /// <summary>
        /// Initialize the performance cache system
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;
            
            ClearCache();
            PrewarmCollectionPools();
            _isInitialized = true;
            
            DebugManager.LogPerformance("[PerformanceCache] Initialized with collection pools");
        }
        
        /// <summary>
        /// Pre-warm collection pools to avoid allocations during gameplay
        /// PERFORMANCE OPTIMIZED: Increased pool sizes for better performance
        /// </summary>
        private static void PrewarmCollectionPools()
        {
            // Pre-warm Vector3Int lists (commonly used for match positions)
            // Increased from 10 to 15 for better coverage
            for (int i = 0; i < 15; i++)
            {
                _vector3IntListPool.Enqueue(new List<Vector3Int>(64)); // Increased capacity from 32 to 64
            }

            // Pre-warm Gem lists (used for match processing)
            // Increased from 8 to 12 for better coverage
            for (int i = 0; i < 12; i++)
            {
                _gemListPool.Enqueue(new List<GemNew>(32)); // Increased capacity from 16 to 32
            }

            // Pre-warm HashSets (used for unique position tracking)
            // Increased from 6 to 10 for better coverage
            for (int i = 0; i < 10; i++)
            {
                _vector3IntHashSetPool.Enqueue(new HashSet<Vector3Int>(64)); // Added initial capacity
            }

            // Pre-warm small dictionaries (used for temporary gem layouts)
            // Increased from 4 to 8 for better coverage
            for (int i = 0; i < 8; i++)
            {
                _gemDictionaryPool.Enqueue(new Dictionary<Vector3Int, GemNew>(128)); // Increased capacity from 64 to 128
            }
        }
        
        #endregion
        
        #region UI Component Caching

        /// <summary>
        /// PERFORMANCE OPTIMIZED: Get cached GameUIDocument instance (manually assigned, no Find calls)
        /// </summary>
        public static GameUIDocument GetGameUIDocument()
        {
            return _cachedGameUIDocument;
        }

        /// <summary>
        /// PERFORMANCE OPTIMIZED: Get cached MovesWarningAnimation instance (manually assigned, no Find calls)
        /// </summary>
        public static MovesWarningAnimation GetMovesWarningAnimation()
        {
            return _cachedMovesWarning;
        }

        /// <summary>
        /// PERFORMANCE OPTIMIZED: Manually assign UI references (call this from inspector-assigned components)
        /// This eliminates ALL FindFirstObjectByType calls for maximum performance
        /// GameUI removed - obsolete, using GameUIDocument only
        /// </summary>
        public static void AssignUIReferences(GameUIDocument gameUIDocument, MovesWarningAnimation movesWarning)
        {
            _cachedGameUIDocument = gameUIDocument;
            _cachedMovesWarning = movesWarning;

            DebugManager.LogPerformance("[PerformanceCache] UI references manually assigned - zero Find calls needed (GameUI removed - obsolete)");
        }
        
        /// <summary>
        /// Generic component caching with type safety
        /// </summary>
        public static T GetComponent<T>() where T : Component
        {
            var type = typeof(T);
            if (_componentCache.TryGetValue(type, out var cached))
            {
                return cached as T;
            }
            
            var component = Object.FindFirstObjectByType<T>();
            if (component != null)
            {
                _componentCache[type] = component;
                DebugManager.LogPerformance($"[PerformanceCache] Cached {type.Name} reference");
            }
            return component;
        }
        
        #endregion
        
        #region Collection Pooling
        
        /// <summary>
        /// Get a pooled Vector3Int list, reducing GC allocations
        /// </summary>
        public static List<Vector3Int> GetVector3IntList()
        {
            if (_vector3IntListPool.TryDequeue(out var list))
            {
                list.Clear();
                return list;
            }
            return new List<Vector3Int>(32);
        }
        
        /// <summary>
        /// Return a Vector3Int list to the pool for reuse
        /// </summary>
        public static void ReturnVector3IntList(List<Vector3Int> list)
        {
            if (list != null && list.Count < 1000) // Prevent memory bloat
            {
                list.Clear();
                _vector3IntListPool.Enqueue(list);
            }
        }
        
        /// <summary>
        /// Get a pooled GemNew list, reducing GC allocations
        /// </summary>
        public static List<GemNew> GetGemList()
        {
            if (_gemListPool.TryDequeue(out var list))
            {
                list.Clear();
                return list;
            }
            return new List<GemNew>(16);
        }
        
        /// <summary>
        /// Return a GemNew list to the pool for reuse
        /// </summary>
        public static void ReturnGemList(List<GemNew> list)
        {
            if (list != null && list.Count < 500) // Prevent memory bloat
            {
                list.Clear();
                _gemListPool.Enqueue(list);
            }
        }
        
        /// <summary>
        /// Get a pooled Vector3Int HashSet, reducing GC allocations
        /// </summary>
        public static HashSet<Vector3Int> GetVector3IntHashSet()
        {
            if (_vector3IntHashSetPool.TryDequeue(out var hashSet))
            {
                hashSet.Clear();
                return hashSet;
            }
            return new HashSet<Vector3Int>();
        }
        
        /// <summary>
        /// Return a Vector3Int HashSet to the pool for reuse
        /// </summary>
        public static void ReturnVector3IntHashSet(HashSet<Vector3Int> hashSet)
        {
            if (hashSet != null && hashSet.Count < 1000) // Prevent memory bloat
            {
                hashSet.Clear();
                _vector3IntHashSetPool.Enqueue(hashSet);
            }
        }
        
        /// <summary>
        /// Get a pooled Gem Dictionary for temporary operations, reducing GC allocations
        /// </summary>
        public static Dictionary<Vector3Int, GemNew> GetGemDictionary()
        {
            if (_gemDictionaryPool.TryDequeue(out var dict))
            {
                dict.Clear();
                return dict;
            }
            return new Dictionary<Vector3Int, GemNew>(64);
        }
        
        /// <summary>
        /// Return a Gem Dictionary to the pool for reuse
        /// </summary>
        public static void ReturnGemDictionary(Dictionary<Vector3Int, GemNew> dict)
        {
            if (dict != null && dict.Count < 500) // Prevent memory bloat
            {
                dict.Clear();
                _gemDictionaryPool.Enqueue(dict);
            }
        }
        
        #endregion

        #region Ultra-Fast Collections (Zero Allocation)

        /// <summary>
        /// ULTRA-FAST: Get pre-allocated Vector3Int list (zero allocation, always available)
        /// WARNING: This is a shared instance - clear it before use and don't store references
        /// </summary>
        public static List<Vector3Int> GetUltraFastVector3IntList()
        {
            _ultraFastVector3IntList.Clear();
            return _ultraFastVector3IntList;
        }

        /// <summary>
        /// ULTRA-FAST: Get pre-allocated HashSet (zero allocation, always available)
        /// WARNING: This is a shared instance - clear it before use and don't store references
        /// </summary>
        public static HashSet<Vector3Int> GetUltraFastHashSet()
        {
            _ultraFastHashSet.Clear();
            return _ultraFastHashSet;
        }

        /// <summary>
        /// ULTRA-FAST: Get pre-allocated Gem list (zero allocation, always available)
        /// WARNING: This is a shared instance - clear it before use and don't store references
        /// </summary>
        public static List<GemNew> GetUltraFastGemList()
        {
            _ultraFastGemList.Clear();
            return _ultraFastGemList;
        }

        /// <summary>
        /// ULTRA-FAST: Get pre-allocated gem position list (zero allocation, always available)
        /// WARNING: This is a shared instance - clear it before use and don't store references
        /// </summary>
        public static List<(Vector3Int pos, GemNew gem)> GetUltraFastGemPositionList()
        {
            _ultraFastGemPositionList.Clear();
            return _ultraFastGemPositionList;
        }

        /// <summary>
        /// ULTRA-FAST: Get pre-allocated UniTask list (zero allocation, always available)
        /// WARNING: This is a shared instance - clear it before use and don't store references
        /// </summary>
        public static List<UniTask> GetUltraFastUniTaskList()
        {
            _ultraFastUniTaskList.Clear();
            return _ultraFastUniTaskList;
        }

        #endregion

        #region Cache Management
        
        /// <summary>
        /// Clear all cached references (call when scene changes)
        /// </summary>
        public static void ClearCache()
        {
            _cachedGameUIDocument = null;
            _cachedMovesWarning = null;
            _componentCache.Clear();

            DebugManager.LogPerformance("[PerformanceCache] Cache cleared (GameUI removed - obsolete)");
        }

        /// <summary>
        /// Force refresh of a specific UI component cache
        /// </summary>
        public static void RefreshUICache()
        {
            _cachedGameUIDocument = null;
            _cachedMovesWarning = null;

            // Note: Manual assignment required - no auto-finding for maximum performance
            DebugManager.LogPerformance("[PerformanceCache] UI cache cleared - manual re-assignment required");
        }
        
        /// <summary>
        /// Get cache statistics for debugging
        /// </summary>
        public static string GetCacheStats()
        {
            return $"[PerformanceCache] Stats - " +
                   $"Vector3IntLists: {_vector3IntListPool.Count}, " +
                   $"GemLists: {_gemListPool.Count}, " +
                   $"HashSets: {_vector3IntHashSetPool.Count}, " +
                   $"Dictionaries: {_gemDictionaryPool.Count}, " +
                   $"Components: {_componentCache.Count}";
        }
        
        #endregion
    }
}
