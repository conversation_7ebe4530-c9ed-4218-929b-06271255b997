using PrimeTween;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.Tilemaps;
using System.Threading;
using System.Linq;
using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using System;
using Random = UnityEngine.Random;
using System.Runtime.InteropServices;
using OwnMatch3.Utils;

// ULTRA-FAST OPTIMIZATION: Simple struct for gem movement data
public struct GemMove
{
    public Vector3Int from;
    public Vector3Int to;
}

public partial class Match3Board : MonoBehaviour
{
    [Header("Configuration")]
    public Tilemap GemsTilemap;
    public TileBase gemPlacer;
    public GemNew[] gemTypes;
    public ObstacleTileData[] obstacleTileMappings;
    public GemTileData[] gemTileMappings;
    [field: SerializeField]public float fallSpeed { get; set; } = 15f; // Tiles per second
    public float tweenDuration = 0.2f;

    [Header("Decorative Tilemaps")]
    public Tilemap BackgroundTilemap;
    public TileBase BackgroundFillTile;
    
    [Header("Background Sprite")]
    public Sprite backgroundSprite; // Assign your background sprite in inspector
    private SpriteRenderer backgroundSpriteRenderer;
    
    // Game state and objectives
    [Header("Game Objectives")]
    public int totalMoves = 30;
    public int movesRemaining;
    public List<Goal> goals = new List<Goal>();
    public bool isGameComplete = false;
    public bool isGameOver = false;

    // Hint system
    [Header("Hint System")]
    public float hintInactivityTime = 5f; // Seconds of inactivity before showing hint
    public bool showHints = true;
    public bool isShowingHint = false;
    private Vector3Int? hintStartPos = null;
    private Vector3Int? hintEndPos = null;
    
    // Performance optimization: Throttle hint calculations
    private CancellationTokenSource _hintTimerCts;
    private bool isFindingHint;

    private readonly Dictionary<int, List<int>> _cachedColumnY = new();

    // Core data structures - ottimizzate
    [SerializeField] private List<MatchShape> matchShapes = new List<MatchShape>();
    private Dictionary<Vector3Int, GemNew> _gems = new();

    public Dictionary<Vector3Int, GemNew> gems
    {
        get => _gems;
        set => _gems = value;
    }

    // Override dictionary access to track ice gem removals
    public void RemoveGemDirectly(Vector3Int pos, string caller = "Unknown")
    {
        if (_gems.ContainsKey(pos))
        {
            var gem = _gems[pos];

            // Track Ice gem removals specifically
            if (obstacles.ContainsKey(pos))
            {
                var obstacle = obstacles[pos];
                if (obstacle.Type == ObstacleType.Ice || obstacle.Type == ObstacleType.IceAdjacentBreakable)
                {
                    if (!obstacle.IsDestroyed)
                    {
                        return; // Block the removal
                    }
                }
            }

            _gems.Remove(pos);
        }
    }

    // Method to safely remove gems with Ice tracking
    public bool RemoveGemWithTracking(Vector3Int pos, string caller = "Unknown")
    {
        if (_gems.ContainsKey(pos))
        {
            var gem = _gems[pos];

            // Track Ice gem removals specifically
            if (obstacles.ContainsKey(pos))
            {
                var obstacle = obstacles[pos];
                if (obstacle.Type == ObstacleType.Ice || obstacle.Type == ObstacleType.IceAdjacentBreakable)
                {
                    if (!obstacle.IsDestroyed)
                    {
                        return false;
                    }
                }
            }

            return _gems.Remove(pos);
        }
        return false;
    }

    // Debug method to check Ice obstacle status
    [System.Diagnostics.Conditional("UNITY_EDITOR")]
    public void DebugIceObstacleStatus()
    {
        foreach (var obstacle in obstacles)
        {
            if (obstacle.Value.Type == ObstacleType.Ice || obstacle.Value.Type == ObstacleType.IceAdjacentBreakable)
            {
                var pos = obstacle.Key;
                bool hasGemInDict = gems.ContainsKey(pos);
                bool hasHiddenGem = obstacle.Value.hiddenGem != null;
                bool hiddenGemActive = hasHiddenGem && obstacle.Value.hiddenGem.gameObject.activeSelf;

            }
        }
    }

    public HashSet<Vector3Int> validPositions = new();
    readonly HashSet<Vector3Int> pendingRefill = new(); // Track positions that need refill

    // Cached references
    Camera mainCamera;
    Mouse mouse;
    CancellationTokenSource cts; // For managing async operations

    // State management
    [HideInInspector]
    public bool isResolvingMatches = false;
    [HideInInspector]
    public bool isInitializing = false; // CRITICAL FIX: Separate flag for initialization
    bool isShuffling = false; // Flag to prevent actions during shuffle animation
    [HideInInspector]
    public bool needResolve = false;
    public bool isTransitioning = false; // Public to be set by LevelLoader
    bool isSwapping = false; // Track if a swap animation is in progress

    public Dictionary<Vector3Int, ObstacleNew> obstacles = new();

    // Stores gem types involved in the last resolved match – filled by BoardMatchResolver.
    [HideInInspector]
    public List<GemType> lastMatchedGemTypes = new();

    // Current combo chain level (incremented each cascade)
    public int ComboChainLevel { get; set; } = 0;

    // True only for the very first ClearMatchesAsync batch after a player swap
    public bool IsFirstMatchBatch { get; set; } = false;

    // Tracks how many successive ClearMatchesAsync cycles have produced matches since the last player swap.
    public int CascadeIndex { get; set; } = 0;

    // Current level's star thresholds (set when level is loaded)
    public StarThresholds CurrentStarThresholds { get; private set; }

    Vector3Int? dragStartCell = null;
    PlayerInput input;


    // Cached directions per evitare allocazioni
    static readonly Vector3Int[] DIRECTIONS = { Vector3Int.right, Vector3Int.up, Vector3Int.left, Vector3Int.down };

    public static GemType? lastColorBombTarget = null;

    [Header("Legacy Tilemap Frame (optional)")]
    [Tooltip("Enable to draw the old Tilemap-based frame. Disable when using SpriteShape outline instead.")]
    public bool enableTilemapFrame = true;

    public static Match3Board Instance;

    public UIManager uiManager;

    public GemAttractorUI gemAttractorUI; // Assegna in Inspector

    [StructLayout(LayoutKind.Sequential)]
    private struct GemMove
    {
        public Vector3Int from;
        public Vector3Int to;
    }

    [BurstCompile]
    [StructLayout(LayoutKind.Sequential)]
    private struct CalculateFallJob : IJob
    {
        [ReadOnly]
        public NativeArray<Vector3Int> ColumnPositions;
        [ReadOnly]
        public NativeHashMap<Vector3Int, bool> GemExistsMap;
        [ReadOnly]
        public NativeHashMap<Vector3Int, bool> ObstacleBlocksFallMap;
        
        public NativeList<GemMove> Moves;

        public void Execute()
        {
            if (ColumnPositions.Length < 2) return;

            int emptyBelowIndex = -1;

            // Dal basso verso l'alto
            for (int i = 0; i < ColumnPositions.Length; i++)
            {
                var currentPos = ColumnPositions[i];

                // Se la posizione è bloccata da ostacolo, resetta
                if (ObstacleBlocksFallMap.ContainsKey(currentPos) && ObstacleBlocksFallMap[currentPos])
                {
                    emptyBelowIndex = -1;
                    continue;
                }

                if (GemExistsMap.ContainsKey(currentPos))
                {
                    if (emptyBelowIndex != -1)
                    {
                        var fallToPos = ColumnPositions[emptyBelowIndex];
                        Moves.Add(new GemMove { from = currentPos, to = fallToPos });
                        emptyBelowIndex++;
                    }
                }
                else
                {
                    if (emptyBelowIndex == -1)
                        emptyBelowIndex = i;
                }
            }
        }
    }

    private NativeArray<HintMove> _hintResult;
    private JobHandle _hintJobHandle;

    private void Awake()
    {
        // Set the singleton reference early for other systems (e.g., bonuses) that cache it in Awake/Start.
        Instance = this;

        input = new PlayerInput();
        mainCamera = Camera.main;
        mouse = Mouse.current;
        cts = new CancellationTokenSource();

        _matchFinder = new MatchFinder(this);
        
        // Initialize background sprite
        InitializeBackgroundSprite();
    }

    void OnEnable() {
        input.Enable();
        isResolvingMatches = false; // SAFETY RESET
        isInitializing = false;     // SAFETY RESET
        isSwapping = false;         // SAFETY RESET
    }
    void OnDisable()
    {
        input.Disable();
        // Cancel any pending async operations when the object is disabled or destroyed.
        cts?.Cancel();
        cts?.Dispose();
        cts = null;
        _hintTimerCts?.Cancel();
        _hintTimerCts?.Dispose();
        _hintTimerCts = null;
    }

    public void Start()
    {
        _matchFinder.CacheShapeData(matchShapes, bonusGems);
        _hintResult = new NativeArray<HintMove>(1, Allocator.Persistent);

        Application.targetFrameRate = 60;
        PrimeTweenConfig.warnEndValueEqualsCurrent = false;
        PrimeTweenConfig.SetTweensCapacity(400);

        // PERFORMANCE OPTIMIZATION: Initialize performance cache system for reduced GC allocations
        OwnMatch3.Utils.PerformanceCache.Initialize();

        ObjectPooler.InitializeObjectPooler();
        //InitializeFromTilemap();
        CenterCameraToBounds();

        // Notify camera manager that board is ready
        var cameraManager = FindFirstObjectByType<CameraManager>();
        if (cameraManager != null)
        {
            cameraManager.OnBoardReady();
        }

        ClearMatchesAtStart();
        CacheColumnData();
        InitializeGoals();
        InitializeHintSystem();
    }

    private void CacheColumnData()
    {
        _cachedColumnY.Clear();
        if (validPositions == null || validPositions.Count == 0) return;

        foreach (var pos in validPositions)
        {
            if (!_cachedColumnY.ContainsKey(pos.x))
            {
                _cachedColumnY[pos.x] = new List<int>();
            }
            _cachedColumnY[pos.x].Add(pos.y);
        }

        foreach (var key in _cachedColumnY.Keys)
        {
            _cachedColumnY[key].Sort();
        }
    }

    void Update()
    {
        HandleInput();
    }


    public void MarkCellForRefill(Vector3Int pos)
    {
        // Check if already in pending refill to avoid duplicates
        if (pendingRefill.Contains(pos))
        {
            return;
        }

        // CRITICAL FIX: Enhanced check for Ice destruction scenarios
        // When Ice is destroyed, the hidden gem remains and should NOT be refilled
        bool hasExistingGem = gems.ContainsKey(pos);
        bool wasRecentlyFreedFromIce = hasExistingGem && !IsGemHiddenByIce(pos) &&
                                      obstacles.ContainsKey(pos) && obstacles[pos].IsDestroyed;

        if (wasRecentlyFreedFromIce)
        {
            return;
        }

        // Only mark for refill if it's valid, empty, and not blocked by obstacles
        if (validPositions.Contains(pos) && !hasExistingGem && CanGemFallHere(pos))
        {
            pendingRefill.Add(pos);
        }
    }

    /// <summary>
    /// Comprehensive validation and emergency fix for board integrity issues
    /// </summary>
    public async UniTask<bool> ValidateAndFixBoardIntegrity()
    {
        bool fixesApplied = false;

        // Find all positions that should have gems but don't
        var missingGemPositions = validPositions.Where(pos =>
            !gems.ContainsKey(pos) &&
            !IsCellBlockedByObstacle(pos) &&
            CanGemFallHere(pos)
        ).ToList();

        if (missingGemPositions.Count > 0)
        {
            var bounds = GetValidBounds();
            var emergencyTasks = new List<UniTask>();

            foreach (var pos in missingGemPositions)
            {
                // Create gem at high spawn position
                var newGem = CreateNewGem(pos, bounds.yMax + 15);
                gems[pos] = newGem;

                // Animate falling
                float distance = Mathf.Abs(pos.y - (bounds.yMax + 15));
                float duration = distance / fallSpeed;
                emergencyTasks.Add(Tween.Position(newGem.transform,
                    new Vector3(pos.x + 0.5f, pos.y + 0.5f, 0),
                    duration, Ease.OutBounce).ToYieldInstruction().ToUniTask(cancellationToken: cts.Token));

                fixesApplied = true;
            }

            if (emergencyTasks.Count > 0)
                await UniTask.WhenAll(emergencyTasks);
        }

        // Check for overlapping gems by examining actual world positions
        var positionGroups = gems.Values.GroupBy(gem => gem.CurrentCell).Where(g => g.Count() > 1).ToList();

        if (positionGroups.Count > 0)
        {
            foreach (var group in positionGroups)
            {
                var pos = group.Key;
                var gemsAtPosition = group.ToList();

                // Keep only the first gem, destroy and remove the rest from the dictionary
                for (int i = 1; i < gemsAtPosition.Count; i++)
                {
                    var gemToRemove = gemsAtPosition[i];

                    // Find and remove from gems dictionary
                    var keysToRemove = gems.Where(kvp => kvp.Value == gemToRemove).Select(kvp => kvp.Key).ToList();
                    foreach (var keyToRemove in keysToRemove)
                    {
                        RemoveGemWithTracking(keyToRemove, "Board Integrity Fix");
                    }

                    await ObjectPooler.ResetPooledObject(gemToRemove.gameObject);
                }

                fixesApplied = true;
            }
        }

        return fixesApplied;
    }

    /// <summary>
    /// Check if there are any active bonus activities that should prevent hints
    /// </summary>
    private bool HasActiveBonusActivities()
    {
        return BonusActivityTracker.Instance != null && BonusActivityTracker.Instance.HasActiveBonusActivities();
    }


    public async UniTask<bool> ApplyGravityAndRefill()
    {
        // PERFORMANCE MONITORING: Track execution time and GC allocations
        using var timer = this.StartPerformanceTimer("ApplyGravityAndRefill");

        // Hide any active hints since the board is about to change
        OnPlayerActivity();

        CacheColumnData();
        var bounds = GetValidBounds();
        bool wasChanged = false;
        int gemsActuallyMoved = 0;

        // CRITICAL FIX: Use individual collections instead of shared ultra-fast ones
        // The shared collections were causing data corruption between columns
        var allMoves = new List<Vector3Int>(64); // Individual list for this call
        var processedColumns = new HashSet<int>(); // Track processed X coordinates

        foreach (var pos in validPositions)
        {
            int x = pos.x;
            if (processedColumns.Contains(x)) continue; // Already processed this column
            processedColumns.Add(x);

            // OPTIMIZED: Direct column processing without LINQ or Jobs
            CalculateColumnFallsDirect(x, bounds, allMoves);
        }

        // SAFETY CHECK: Ensure we have valid move data
        if (allMoves.Count % 2 != 0)
        {
            return false;
        }

        // ULTRA-FAST OPTIMIZATION: Process moves directly without Job System overhead
        int moveCount = allMoves.Count / 2; // Each move takes 2 positions (from, to)

        // SAFETY CHECK: Prevent infinite loops and memory issues
        if (moveCount > 100) // More conservative limit for safety
        {
            moveCount = 100;
        }

        for (int i = 0; i < moveCount; i++)
        {
            var move = GetGemMoveFromList(allMoves, i); // Helper method to extract move data
            if (move.from == Vector3Int.zero && move.to == Vector3Int.zero) continue; // Invalid move
            if (!gems.TryGetValue(move.from, out var gemToMove)) continue;
            if (gems.ContainsKey(move.to)) continue;

            RemoveGemWithTracking(move.from, "Gravity System");
            gems[move.to] = gemToMove;
            gemToMove.CurrentCell = move.to;
            float distance = Vector3Int.Distance(move.from, move.to);
            float duration = distance / fallSpeed;

            // ULTRA-FAST: Skip complex tween handling for now to focus on GC reduction
            // The tween will be handled by a simpler system
            var tween = Tween.Position(gemToMove.transform, new Vector3(move.to.x + 0.5f, move.to.y + 0.5f), duration, Ease.OutQuad);

            gemsActuallyMoved++;
            wasChanged = true;
        }

        // ULTRA-FAST OPTIMIZATION: Handle tween awaiting more efficiently
        // Note: For now, skip the complex UniTask handling to focus on GC reduction

        // --- Fase 2: Refill delle celle vuote (FIXED) ---
        int gemsActuallyCreated = 0;
        int startYBase = bounds.yMax + 1;

        // CRITICAL FIX: Only find empty positions that can actually be filled by falling gems
        var allEmptyPositions = new HashSet<Vector3Int>(pendingRefill);

        foreach (var pos in validPositions)
        {
            if (!gems.ContainsKey(pos) && !IsCellBlockedByObstacle(pos))
            {
                // FIXED LOGIC: Only create gems at the top of the board to avoid obstacle conflicts
                if (pos.y == bounds.yMax)
                {
                    allEmptyPositions.Add(pos);
                }
                // For positions below the top, skip them to avoid obstacle contradictions
            }
        }


        // Process each column for refill (without LINQ)
        var processedRefillColumns = new HashSet<int>();

        foreach (var pos in validPositions)
        {
            int x = pos.x;
            if (processedRefillColumns.Contains(x)) continue;
            processedRefillColumns.Add(x);

            ProcessColumnRefillDirect(x, bounds, allEmptyPositions, ref gemsActuallyCreated, ref wasChanged, startYBase);
        }

        // SIMPLE FIX: Apply direct column-by-column gravity after refill
        if (gemsActuallyCreated > 0)
        {

            // Process each column independently with simple gravity and await animations
            var gravityTasks = new List<UniTask>();
            for (int x = bounds.xMin; x <= bounds.xMax; x++)
            {
                var task = ApplySimpleColumnGravityAsync(x, bounds);
                gravityTasks.Add(task);
            }

            // Wait for all gravity animations to complete
            if (gravityTasks.Count > 0)
            {
                await UniTask.WhenAll(gravityTasks);
            }
        }

        // Clear pendingRefill after processing
        pendingRefill.Clear();

        // ULTRA-FAST OPTIMIZATION: Skip complex validation for now to focus on performance
        return wasChanged;
    }

    private async UniTask ApplySimpleColumnGravityAsync(int x, (int xMin, int xMax, int yMin, int yMax) bounds)
    {
        // PERFORMANCE OPTIMIZED: Use ultra-fast cached collections
        var gemsInColumn = PerformanceCache.GetUltraFastGemPositionList();

        for (int y = bounds.yMax; y >= bounds.yMin; y--)
        {
            var pos = new Vector3Int(x, y, 0);
            if (gems.ContainsKey(pos))
            {
                gemsInColumn.Add((pos, gems[pos]));
            }
        }

        if (gemsInColumn.Count == 0) return;

        // PERFORMANCE OPTIMIZED: Use ultra-fast cached collections
        var validPositionsInColumn = PerformanceCache.GetUltraFastVector3IntList();
        for (int y = bounds.yMin; y <= bounds.yMax; y++)
        {
            var pos = new Vector3Int(x, y, 0);
            if (validPositions.Contains(pos) && !IsCellBlockedByObstacle(pos))
            {
                validPositionsInColumn.Add(pos);
            }
        }

        // PERFORMANCE: Clear gems efficiently
        foreach (var (pos, gem) in gemsInColumn)
        {
            gems.Remove(pos);
        }

        // PERFORMANCE OPTIMIZED: Use cached collections to avoid GC allocations
        var animationTasks = new List<UniTask>(gemsInColumn.Count);

        // PERFORMANCE: Process gems with minimal allocations
        int gemCount = Math.Min(gemsInColumn.Count, validPositionsInColumn.Count);
        for (int i = 0; i < gemCount; i++)
        {
            var targetPos = validPositionsInColumn[i]; // Bottom-most position first
            var (oldPos, gem) = gemsInColumn[gemsInColumn.Count - 1 - i]; // Bottom-most gem first

            gems[targetPos] = gem;
            gem.CurrentCell = targetPos;

            // OPTIMIZED: Calculate animation parameters efficiently
            float distance = Vector3Int.Distance(oldPos, targetPos);
            float baseDuration = distance / fallSpeed;
            float staggerDelay = i * 0.05f; // Small delay between gems for visual appeal

            // PERFORMANCE: Create optimized animation task
            var animTask = AnimateGemFall(gem, targetPos, baseDuration, staggerDelay);
            animationTasks.Add(animTask);
        }

        // PERFORMANCE: Wait for all animations in this column to complete
        if (animationTasks.Count > 0)
        {
            await UniTask.WhenAll(animationTasks);
        }
    }

    private async UniTask AnimateGemFall(GemNew gem, Vector3Int targetPos, float duration, float delay)
    {
        // PROFESSIONAL: Staggered delay for visual appeal
        if (delay > 0)
        {
            await Tween.Delay(delay);
        }

        var targetWorldPos = new Vector3(targetPos.x + 0.5f, targetPos.y + 0.5f, 0);

        // PROFESSIONAL: Single, fast, juicy animation
        await AnimateProfessionalFall(gem, targetWorldPos, duration);
    }

    private async UniTask AnimateProfessionalFall(GemNew gem, Vector3 targetPos, float duration)
    {
        // CLEAN: Simple, direct fall with no bouncing or flashing effects

        // Just a simple, clean fall animation - no arcs, no rotation, no impact effects
        await Tween.Position(gem.transform, targetPos, duration, Ease.OutQuad);
    }

    private GemNew CreateNewGem(Vector3Int position, int startY)
    {
        var randomGemPrefab = GetRandomGem();
        if (randomGemPrefab == null)
        {
            return null;
        }

        ObjectPooler.PreWarmPool(randomGemPrefab, 10, "Gems");
        Vector3 startPos = new Vector3(position.x + 0.5f, startY + 0.5f, 0);

        GemNew newGem = ObjectPooler.GetPooledObject(randomGemPrefab, startPos, Quaternion.identity, transform, "Gems");
        if (newGem == null)
        {
            return null;
        }

        newGem.CurrentCell = position;

        // JUICY: Animate gem creation with pop-in effect
        AnimateGemCreation(newGem);

        return newGem;
    }

    private void AnimateGemCreation(GemNew gem)
    {
        // CLEAN: Simple, clean creation animation without any shaking effects
        gem.transform.localScale = Vector3.zero;

        // Simple pop-in animation - no rotation, no overshoot
        Tween.Scale(gem.transform, Vector3.one, 0.1f, Ease.OutBack);
    }

    private void AddMatchFeedbackAnimations(List<Vector3Int> matchedPositions, List<GemType> matchedGemTypes)
    {
        // SMOOTH FLOW: Only subtle particle effects - no blocking, no annoying effects
        if (matchedPositions.Count > 0)
        {
            Vector3 centerPos = Vector3.zero;
            foreach (var pos in matchedPositions)
            {
                centerPos += new Vector3(pos.x + 0.5f, pos.y + 0.5f, 0);
            }
            centerPos /= matchedPositions.Count;

        }

        // Return immediately - no delays, no blocking effects
    }

    private GemNew GetRandomGem()
    {
        var randomGemPrefab = gemTypes[Random.Range(0, gemTypes.Length)];
        return randomGemPrefab;
    }

    (int xMin, int xMax, int yMin, int yMax) GetValidBounds()
    {
        if (validPositions.Count == 0) return (0, 0, 0, 0);
        int minX = int.MaxValue, maxX = int.MinValue;
        int minY = int.MaxValue, maxY = int.MinValue;
        foreach (var pos in validPositions)
        {
            if (pos.x < minX) minX = pos.x;
            if (pos.x > maxX) maxX = pos.x;
            if (pos.y < minY) minY = pos.y;
            if (pos.y > maxY) maxY = pos.y;
        }
        return (minX, maxX, minY, maxY);
    }

    public bool IsGemHiddenByIce(Vector3Int pos)
    {
        return obstacles.TryGetValue(pos, out var obstacle) &&
               !obstacle.IsDestroyed &&
               (obstacle.Type == ObstacleType.Ice || obstacle.Type == ObstacleType.IceAdjacentBreakable);
    }

    /// <summary>
    /// Check if a gem is hidden by any obstacle (Ice, IceAdjacentBreakable, or SwappableObstacle)
    /// </summary>
    public bool IsGemHiddenByObstacle(Vector3Int pos)
    {
        return obstacles.TryGetValue(pos, out var obstacle) &&
               !obstacle.IsDestroyed &&
               obstacle.HidesGem;
    }

    /// <summary>
    /// Check if a gem is hidden by an Ice obstacle that prevents matching
    /// CORRECT BEHAVIOR:
    /// - Ice obstacles ('I') ALLOW their hidden gems to participate in matches (to damage the ice)
    /// - IceAdjacentBreakable obstacles ('J') PREVENT their hidden gems from matching (only adjacent matches damage them)
    /// </summary>
    public bool IsGemUnmatchableByIce(Vector3Int pos)
    {
        bool result = obstacles.TryGetValue(pos, out var obstacle) &&
                     !obstacle.IsDestroyed &&
                     obstacle.Type == ObstacleType.IceAdjacentBreakable; // Only IceAdjacentBreakable prevents matching

        // Enhanced debugging for Ice gem matching consistency
        if (obstacles.TryGetValue(pos, out var obs) && !obs.IsDestroyed &&
            (obs.Type == ObstacleType.Ice || obs.Type == ObstacleType.IceAdjacentBreakable))
        {
        }

        return result;
    }

    /// <summary>
    /// Check if a gem is hidden by an obstacle that prevents swapping
    /// CORRECT BEHAVIOR: Ice, IceAdjacentBreakable, and AdjacentBreakable all prevent swapping
    /// Ice gems cannot be swapped - they can only be matched when other gems create matches that include them
    /// </summary>
    public bool IsGemUnswappableByObstacle(Vector3Int pos)
    {
        bool result = obstacles.TryGetValue(pos, out var obstacle) &&
                     !obstacle.IsDestroyed &&
                     (obstacle.Type == ObstacleType.Ice ||
                      obstacle.Type == ObstacleType.IceAdjacentBreakable ||
                      obstacle.Type == ObstacleType.AdjacentBreakable);
                     // All these obstacle types prevent swapping

        // Enhanced debugging for Ice gem swapping
        if (obstacles.TryGetValue(pos, out var obs) && !obs.IsDestroyed &&
            (obs.Type == ObstacleType.Ice || obs.Type == ObstacleType.IceAdjacentBreakable))
        {
        }

        return result;
    }

    void InitializeGoals()
    {
        movesRemaining = totalMoves;

        // Reset score for new level
        if (ScoreManager.Instance != null)
        {
            ScoreManager.Instance.ResetScore();
        }

        // Initialize all goals
        foreach (var goal in goals)
        {
            goal.Initialize(this);
        }

        // CRITICAL FIX: Set flag to prevent game completion during initialization
        bool wasResolvingMatches = isResolvingMatches;
        isResolvingMatches = true; // Temporarily prevent completion checking

        UpdateAllGoals();

        // Restore the original state
        isResolvingMatches = wasResolvingMatches;
    }

    public void ConsumeMove()
    {
        if (movesRemaining > 0)
        {
            movesRemaining--;

            // Don't check game completion here - it will be checked after cascades complete
            // This prevents premature game over when the final move triggers winning cascades
        }
    }

    public void UpdateAllGoals()
    {
        foreach (var goal in goals)
        {
            goal.UpdateProgress(this);
        }

        // Don't check game completion during match resolution to prevent premature game over
        // Game completion will be checked after all cascades are resolved
        if (!isResolvingMatches)
        {
            CheckGameCompletion();
        }
    }

    public void CancelAllBoardOperations()
    {
        if (cts != null)
        {
            cts.Cancel();
            cts.Dispose();
        }
        cts = new CancellationTokenSource();

        // Reset hint timer CTS as it was linked to the old token
        _hintTimerCts?.Cancel();
        _hintTimerCts?.Dispose();
        _hintTimerCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token);

        // Ensure no lingering hint visuals remain
        HideHint();
    }

    public async void OnMatchCompleted(List<Vector3Int> matchedPositions, List<GemType> matchedGemTypes)
    {
        // SMOOTH FLOW: Add non-blocking match feedback animations
        AddMatchFeedbackAnimations(matchedPositions, matchedGemTypes);

        // Track match for missions
        if (OwnMatch3.UI.MissionsManager.Instance != null)
        {
            OwnMatch3.UI.MissionsManager.Instance.OnMatchMade(matchedPositions.Count);
        }

        // Track gem collection for missions
        if (OwnMatch3.UI.MissionsManager.Instance != null)
        {
            foreach (var gemType in matchedGemTypes)
            {
                // Convert from Match3 GemType to UI GemType
                var uiGemType = ConvertToUIGemType(gemType);
                OwnMatch3.UI.MissionsManager.Instance.OnGemCollected(uiGemType, 1);
            }
        }

        // Raccogli tutti i dati delle gemme che corrispondono ai goal attivi
        var gemData = new List<(Vector3 worldPos, string goalIconName, GemType gemType)>();
        
        foreach (var goal in goals)
        {
            if (goal is CollectGemGoal collectGoal)
            {
                // Verifica se questo goal è ancora attivo (non completato)
                if (!collectGoal.IsCompleted)
                {
                    // Safety check to prevent index out of range errors
                    int maxIndex = Mathf.Min(matchedPositions.Count, matchedGemTypes.Count);
                    for (int i = 0; i < maxIndex; i++)
                    {
                        if (matchedGemTypes[i] == collectGoal.targetGemType)
                        {
                            Vector3 worldPos = matchedPositions[i];
                            string goalIconName = $"gem-icon-{collectGoal.targetGemType}";
                            
                            // Aggiungi l'animazione direttamente
                            if (gemAttractorUI != null)
                            {
                                gemData.Add((worldPos, goalIconName, collectGoal.targetGemType));
                            }
                        }
                    }
                }
            }
            goal.OnMatchCompleted(this, matchedPositions, matchedGemTypes);
        }
        
        // Esegui le animazioni solo se ci sono gemme da animare
        if (gemData.Count > 0 && gemAttractorUI != null)
        {
            if (gemAttractorUI.sequentialSpawning)
            {
                // Usa il sistema sequenziale
                await gemAttractorUI.AnimateMultipleGemsToGoal(gemData);
            }
            else
            {
                // Usa il sistema parallelo
                var tasks = new List<UniTask>();
                foreach (var (worldPos, goalIconName, gemType) in gemData)
                {
                    tasks.Add(gemAttractorUI.AnimateGemToGoal(worldPos, goalIconName, gemType));
                }
                await UniTask.WhenAll(tasks);
            }
        }

        // Don't check game completion during match resolution to prevent premature game over
        // Game completion will be checked after all cascades are resolved
        if (!isResolvingMatches)
        {
            CheckGameCompletion();
        }

        // Aggiorna subito la UI dei goal tramite GameUI
        var gameUI = FindFirstObjectByType<GameUI>();
        gameUI?.UpdateGoalsDisplay();
        // Se usi anche GameUIDocument (UI Toolkit), aggiorna anche quella
        var uiDoc = FindFirstObjectByType<GameUIDocument>();
        uiDoc?.RefreshUI();
    }

    public void OnObstacleDestroyed(Vector3Int position, ObstacleType obstacleType)
    {
        // Add points through ScoreManager for obstacle destruction
        if (ScoreManager.Instance != null)
        {
            ScoreManager.Instance.AddObstaclePoints(obstacleType);
        }

        // Collect obstacle data for attractor animations
        var obstacleData = new List<(Vector3 worldPos, string goalIconName, ObstacleType obstacleType)>();

        foreach (var goal in goals)
        {
            if (goal is DestroyObstacleGoal destroyGoal)
            {
                // Verifica se questo goal è ancora attivo (non completato) e corrisponde al tipo di ostacolo
                if (!destroyGoal.IsCompleted && destroyGoal.targetObstacleType == obstacleType)
                {
                    Vector3 worldPos = new Vector3(position.x + 0.5f, position.y + 0.5f, 0);
                    string goalIconName = $"obstacle-icon-{obstacleType}";

                    // Aggiungi l'animazione dell'ostacolo
                    if (gemAttractorUI != null)
                    {
                        obstacleData.Add((worldPos, goalIconName, obstacleType));
                    }
                }
            }
            goal.OnObstacleDestroyed(this, position, obstacleType);
        }

        // Esegui le animazioni degli ostacoli solo se ci sono ostacoli da animare
        if (obstacleData.Count > 0 && gemAttractorUI != null)
        {
            if (gemAttractorUI.sequentialSpawning)
            {
                // Usa il sistema sequenziale
                gemAttractorUI.AnimateMultipleObstaclesToGoal(obstacleData).Forget();
            }
            else
            {
                // Usa il sistema parallelo
                var tasks = new List<UniTask>();
                foreach (var (worldPos, goalIconName, obstType) in obstacleData)
                {
                    tasks.Add(gemAttractorUI.AnimateObstacleToGoal(worldPos, goalIconName, obstType));
                }
                UniTask.WhenAll(tasks).Forget();
            }
        }

        // Don't check game completion during match resolution to prevent premature game over
        // Game completion will be checked after all cascades are resolved
        if (!isResolvingMatches)
        {
            CheckGameCompletion();
        }
    }

    public void CheckGameCompletion()
    {
        if (isGameComplete || isGameOver) return;

        // Don't check completion if bonus activities are still running
        // This prevents level completion from interrupting bonus animations and effects
        if (BonusActivityTracker.Instance != null && BonusActivityTracker.Instance.HasActiveBonusActivities())
        {
            // Schedule a delayed check once bonus activities complete
            CheckGameCompletionAfterBonusActivities().Forget();
            return;
        }

        // Check if all goals are completed
        bool allGoalsCompleted = true;
        foreach (var goal in goals)
        {
            if (!goal.IsCompleted)
            {
                allGoalsCompleted = false;
                break;
            }
        }

        if (allGoalsCompleted)
        {
            isGameComplete = true;
            DebugManager.LogMatch("Level Complete! All goals achieved!");

            // Preserve moves remaining before bonus sequence consumes them
            int movesBeforeBonus = movesRemaining;

            // Trigger bonus sequence if there are remaining moves
            if (movesRemaining > 0)
            {
                TriggerBonusSequence(movesBeforeBonus).Forget();
            }
            else
            {
                // No bonus sequence, complete immediately
                OnLevelCompleted(movesBeforeBonus);
            }
        }
        else if (movesRemaining <= 0)
        {
            isGameOver = true;
            DebugManager.LogMatch("Game Over! Out of moves!");

            // Trigger game over UI and event
            OnGameOver();
        }
    }

    /// <summary>
    /// Wait for all bonus activities to complete, then check game completion
    /// </summary>
    private async UniTaskVoid CheckGameCompletionAfterBonusActivities()
    {
        if (BonusActivityTracker.Instance != null)
        {
            await BonusActivityTracker.Instance.WaitForAllBonusActivitiesToComplete();
        }

        // Only check completion if the game state hasn't changed
        if (!isGameComplete && !isGameOver && !isTransitioning)
        {
            CheckGameCompletion();
        }
    }

    public bool CanMakeMove()
    {
        return movesRemaining > 0 && !isGameComplete && !isGameOver && !isResolvingMatches;
    }

    /// <summary>
    /// Trigger the bonus sequence for remaining moves
    /// </summary>
    private async UniTaskVoid TriggerBonusSequence(int originalMovesRemaining)
    {
        if (BonusGemManager.Instance == null)
        {
            OnLevelCompleted(originalMovesRemaining);
            return;
        }

        // Start the bonus sequence (this will consume movesRemaining)
        await BonusGemManager.Instance.StartBonusSequence(this, movesRemaining);

        // Complete the level after bonus sequence, using the original moves count
        OnLevelCompleted(originalMovesRemaining);
    }

    /// <summary>
    /// Called when level is completed (after bonus sequence if applicable)
    /// </summary>
    private void OnLevelCompleted(int originalMovesRemaining)
    {
        // Calculate stars earned and score
        int starsEarned = CalculateStarsEarned();
        int finalScore = ScoreManager.Instance != null ? ScoreManager.Instance.CurrentScore : 0;

        // Notify missions manager about level completion and win using original moves count
        if (OwnMatch3.UI.MissionsManager.Instance != null)
        {
            OwnMatch3.UI.MissionsManager.Instance.OnLevelCompleted(starsEarned, finalScore, originalMovesRemaining);
            OwnMatch3.UI.MissionsManager.Instance.OnLevelWon(); // Track win streak

            DebugManager.LogMatch($"[Match3Board] Level completed with {originalMovesRemaining} moves remaining (before bonus sequence)");
        }

        // Show the end game popup with stars and score
        ShowEndGamePopup();

        // Fire level completion event for UI systems (but don't auto-transition)
        LevelCompletedEvent?.Invoke();
    }

    /// <summary>
    /// Check if all goals are completed
    /// </summary>
    private bool AreGoalsCompleted()
    {
        foreach (var goal in goals)
        {
            if (!goal.IsCompleted)
            {
                return false;
            }
        }
        return true;
    }

    /// <summary>
    /// Calculate stars earned based on score and level completion
    /// </summary>
    private int CalculateStarsEarned()
    {
        // Basic star calculation - can be enhanced based on your game's requirements
        if (!AreGoalsCompleted())
        {
            return 0; // No stars if goals not completed
        }

        int currentScore = ScoreManager.Instance != null ? ScoreManager.Instance.CurrentScore : 0;

        // Simple star calculation based on score thresholds
        // You can customize these thresholds or use different criteria
        if (currentScore >= 100000) return 3;
        if (currentScore >= 50000) return 2;
        return 1; // At least 1 star for completing the level
    }

    /// <summary>
    /// Called when game is over (out of moves without completing goals)
    /// </summary>
    private void OnGameOver()
    {
        // Notify missions manager about level loss (resets win streaks)
        if (OwnMatch3.UI.MissionsManager.Instance != null)
        {
            OwnMatch3.UI.MissionsManager.Instance.OnLevelLost();
        }

        // Show the end game popup for game over scenario
        ShowGameOverPopup();

        // Fire game over event for UI systems
        GameOverEvent?.Invoke();
    }

    /// <summary>
    /// Show the end game popup with stars and final score
    /// </summary>
    private void ShowEndGamePopup()
    {
        // Find the EndGamePopupController and show it
        var popupController = FindFirstObjectByType<EndGamePopupController>();
        if (popupController != null)
        {
            popupController.ShowEndGamePopup();
        }
    }

    /// <summary>
    /// Show the game over popup when player runs out of moves
    /// </summary>
    private void ShowGameOverPopup()
    {
        // Find the EndGamePopupController and show it for game over
        var popupController = FindFirstObjectByType<EndGamePopupController>();
        if (popupController != null)
        {
            popupController.ShowGameOverPopup();
        }
    }

    /// <summary>
    /// Event fired when level is completed (including after bonus sequence)
    /// </summary>
    public static event Action LevelCompletedEvent;

    /// <summary>
    /// Event fired when game is over (out of moves without completing goals)
    /// </summary>
    public static event Action GameOverEvent;

    /// <summary>
    /// Convert Match3 GemType to UI GemType for missions
    /// </summary>
    private OwnMatch3.UI.GemType ConvertToUIGemType(GemType match3GemType)
    {
        return match3GemType switch
        {
            GemType.Red => OwnMatch3.UI.GemType.Red,
            GemType.Blue => OwnMatch3.UI.GemType.Blue,
            GemType.Green => OwnMatch3.UI.GemType.Green,
            GemType.Yellow => OwnMatch3.UI.GemType.Yellow,
            GemType.Purple => OwnMatch3.UI.GemType.Purple,
            GemType.Orange => OwnMatch3.UI.GemType.Orange,
            _ => OwnMatch3.UI.GemType.Any
        };
    }

    void InitializeHintSystem()
    {
        isShowingHint = false;
        hintStartPos = null;
        hintEndPos = null;
        
        _hintTimerCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token);
        HintSystemLoop().Forget();
    }

    public void OnPlayerActivity()
    {
        HideHint();
        _hintTimerCts?.Cancel();
        _hintTimerCts?.Dispose();
        _hintTimerCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token);
    }

    private async UniTask HintSystemLoop()
    {
        while (cts != null && !cts.IsCancellationRequested)
        {
            try
            {
                // 1. Wait for the inactivity period. This will be cancelled by player activity.
                await UniTask.Delay(TimeSpan.FromSeconds(hintInactivityTime), cancellationToken: _hintTimerCts.Token);

                // 2. If we're here, the timer completed. Check if we should proceed.
                bool canShowHint = showHints && !isGameComplete && !isGameOver && !isResolvingMatches &&
                                  !isShuffling && !isFindingHint && !isShowingHint && !isInitializing &&
                                  !isSwapping && pendingRefill.Count == 0 && !HasActiveBonusActivities();

                if (!canShowHint)
                {
                    // Debug log why hint is being prevented
                    var reasons = new List<string>();
                    if (!showHints) reasons.Add("hints disabled");
                    if (isGameComplete) reasons.Add("game complete");
                    if (isGameOver) reasons.Add("game over");
                    if (isResolvingMatches) reasons.Add("resolving matches");
                    if (isShuffling) reasons.Add("shuffling");
                    if (isFindingHint) reasons.Add("finding hint");
                    if (isShowingHint) reasons.Add("showing hint");
                    if (isInitializing) reasons.Add("initializing");
                    if (isSwapping) reasons.Add("swapping");
                    if (pendingRefill.Count > 0) reasons.Add($"pending refill ({pendingRefill.Count})");
                    if (HasActiveBonusActivities()) reasons.Add("bonus activities");

                    DebugManager.LogMatch($"Hint prevented: {string.Join(", ", reasons)}");

                    // State is not right for a hint, wait a bit and re-check.
                    await UniTask.Delay(500, cancellationToken: cts.Token); // Use the main CTS here
                    continue;
                }

                // 3. Find a valid move.
                isFindingHint = true;
                var validMove = await FindValidMoveAsync();
                isFindingHint = false;
                
                // 4. Check again if the state changed while we were searching for a hint.
                if (_hintTimerCts.IsCancellationRequested)
                {
                    continue; // Player made a move while we were searching, so just loop.
                }

                // 5. If a move was found, show it.
                if (validMove.HasValue)
                {
                    hintStartPos = validMove.Value.startPos;
                    hintEndPos = validMove.Value.endPos;
                    isShowingHint = true;
                    TriggerHintVisuals(hintStartPos.Value, hintEndPos.Value);
                }
                else
                {
                    DebugManager.LogMatch("No valid move found for hint");
                }
            }
            catch (OperationCanceledException)
            {
                // This is the expected result of player activity.
                // The loop will continue and start a new delay timer.
            }
        }
    }
    
    void HideHint()
    {
        if (!isShowingHint) return;
        isShowingHint = false;
        hintStartPos = null;
        hintEndPos = null;
        ClearHintVisuals();
    }

    void TriggerHintVisuals(Vector3Int startPos, Vector3Int endPos)
    {
        if (gems.TryGetValue(startPos, out var startGem))
        {
            Tween.Scale(startGem.transform, Vector3.one * 1.2f, 0.5f, Ease.OutBack, -1, CycleMode.Yoyo);
        }
        if (gems.TryGetValue(endPos, out var endGem))
        {
            Tween.Scale(endGem.transform, Vector3.one * 1.2f, 0.5f, Ease.OutBack, -1, CycleMode.Yoyo);
        }
    }

    void ClearHintVisuals()
    {
        foreach (var gem in gems.Values)
        {
            Tween.StopAll(gem.transform);
            gem.transform.localScale = Vector3.one;
        }
    }

    public async UniTask<HintMove?> FindValidMoveAsync()
    {
        var gemPositions = new NativeArray<Vector3Int>(gems.Keys.ToArray(), Allocator.TempJob);
        var gemGrid = new NativeHashMap<Vector3Int, int>(gems.Count, Allocator.TempJob);
        var unswappableGrid = new NativeHashMap<Vector3Int, bool>(gems.Count, Allocator.TempJob);

        foreach (var kvp in gems)
        {
            gemGrid.Add(kvp.Key, (int)kvp.Value.GemType);

            // CORRECT BEHAVIOR: For hints, gems must be BOTH matchable AND swappable
            // Ice gems can match but cannot be swapped, so they should not appear in hints
            // Only suggest swaps between gems that can actually be swapped (not blocked by obstacles)
            bool unswappable = kvp.Value.bonus != null || IsGemUnswappableByObstacle(kvp.Key);
            unswappableGrid.Add(kvp.Key, unswappable);
        }

        var job = new FindHintJob
        {
            GemPositions = gemPositions,
            GemGrid = gemGrid,
            UnswappableGrid = unswappableGrid,
            Result = _hintResult
        };

        _hintJobHandle = job.Schedule();
        await _hintJobHandle;

        HintMove result = _hintResult[0];

        gemPositions.Dispose();
        gemGrid.Dispose();
        unswappableGrid.Dispose();

        // CRITICAL FIX: Validate the hint using the same logic as the actual swap system
        if (result.found)
        {
            bool actuallyValid = SwapCreatesMatch(result.startPos, result.endPos);
            if (!actuallyValid)
            {
                return null; // Don't return invalid hints
            }
        }

        return result.found ? result : (HintMove?)null;
    }

    /// <summary>
    /// Call this method when the board grid is expanded to ensure proper camera centering
    /// This method recalculates the camera position and size based on the new board bounds
    /// </summary>
    public void OnBoardExpanded()
    {
        ForceCameraRecalculation();
    }

    /// <summary>
    /// Updates the valid positions set and recalculates camera if needed
    /// Call this when adding new valid positions to the board
    /// </summary>
    public void AddValidPosition(Vector3Int position)
    {
        if (!validPositions.Contains(position))
        {
            validPositions.Add(position);
            // Optionally recalculate camera if this significantly changes the board bounds
            if (validPositions.Count % 10 == 0) // Recalculate every 10 new positions
            {
                RecalculateCameraFit();
            }
        }
    }

    private void OnDestroy()
    {
        if (Instance == this)
        {
            Instance = null;
        }
        if (_hintResult.IsCreated) _hintResult.Dispose();
        _matchFinder.Dispose();
    }

    /// <summary>
    /// Initializes the background sprite renderer
    /// </summary>
    private void InitializeBackgroundSprite()
    {
        if (backgroundSprite == null) return;

        // Create a child GameObject for the background sprite
        GameObject backgroundObject = new GameObject("BackgroundSprite");
        backgroundObject.transform.SetParent(transform);
        backgroundObject.transform.localPosition = Vector3.zero;
        
        // Add SpriteRenderer component
        backgroundSpriteRenderer = backgroundObject.AddComponent<SpriteRenderer>();
        backgroundSpriteRenderer.sprite = backgroundSprite;
        backgroundSpriteRenderer.sortingOrder = -100; // Ensure it's behind everything
        backgroundSpriteRenderer.color = Color.white;
        
        // Set initial size and position
        UpdateBackgroundSpriteToCamera();
    }

    /// <summary>
    /// Updates the background sprite position and scale to match the camera view
    /// </summary>
    private void UpdateBackgroundSpriteToCamera()
    {
        if (backgroundSpriteRenderer == null || mainCamera == null) return;

        // Calculate camera viewport bounds
        float cameraHeight = mainCamera.orthographicSize * 2f;
        float cameraWidth = cameraHeight * mainCamera.aspect;
        
        // Position the background at the camera center
        Vector3 cameraCenter = mainCamera.transform.position;
        backgroundSpriteRenderer.transform.position = new Vector3(cameraCenter.x, cameraCenter.y, cameraCenter.z + 1f); // Slightly in front of camera
        
        // Scale the sprite to cover the entire camera viewport
        float spriteAspect = backgroundSpriteRenderer.sprite.rect.width / backgroundSpriteRenderer.sprite.rect.height;
        float cameraAspect = cameraWidth / cameraHeight;
        
        float scaleX, scaleY;
        if (cameraAspect > spriteAspect)
        {
            // Camera is wider than sprite, scale by width
            scaleX = cameraWidth / backgroundSpriteRenderer.sprite.bounds.size.x;
            scaleY = scaleX;
        }
        else
        {
            // Camera is taller than sprite, scale by height
            scaleY = cameraHeight / backgroundSpriteRenderer.sprite.bounds.size.y;
            scaleX = scaleY;
        }
        
        // Add a small margin to ensure full coverage
        float margin = 0.1f;
        backgroundSpriteRenderer.transform.localScale = new Vector3(scaleX * (1f + margin), scaleY * (1f + margin), 1f);
    }

    // Ritorna true se la cella contiene un ostacolo non distrutto che blocca la caduta delle gemme
    public bool IsCellBlockedByObstacle(Vector3Int pos)
    {
        if (obstacles.TryGetValue(pos, out var obstacle))
        {
            if (!obstacle.IsDestroyed)
            {
                // CRITICAL FIX: SwappableObstacle should NOT block gem falling
                // It acts as a bubble behind gems, so gems should fall through
                if (obstacle.Type == ObstacleType.SwappableObstacle)
                    return false; // SwappableObstacle allows gems to fall through

                // Other obstacles block gem falling
                if (obstacle.Type == ObstacleType.Ice ||
                    obstacle.Type == ObstacleType.IceAdjacentBreakable ||
                    obstacle.Type == ObstacleType.AdjacentBreakable)
                    return true;
            }
        }
        return false;
    }

    // FIXED: Simple and reliable gravity calculation
    private void CalculateColumnFallsDirect(int x, (int xMin, int xMax, int yMin, int yMax) bounds, List<Vector3Int> movesList)
    {
        // Simple approach: For each gem, find the lowest empty position below it
        // IMPORTANT: Check from TOP to BOTTOM (yMax to yMin)
        for (int y = bounds.yMax; y >= bounds.yMin; y--)
        {
            var gemPos = new Vector3Int(x, y, 0);

            // Skip if not a valid position or no gem here
            if (!validPositions.Contains(gemPos) || !gems.ContainsKey(gemPos))
                continue;

            // Find the lowest empty position this gem can fall to
            int lowestY = y;
            for (int checkY = y - 1; checkY >= bounds.yMin; checkY--)
            {
                var checkPos = new Vector3Int(x, checkY, 0);

                // Stop if we hit another gem
                if (gems.ContainsKey(checkPos))
                    break;

                // FIXED: Skip obstacles but continue checking below them
                if (obstacles.ContainsKey(checkPos) && IsCellBlockedByObstacle(checkPos))
                    continue; // Skip this position but keep checking below

                // FIXED: Skip invalid positions but continue checking below them
                if (!validPositions.Contains(checkPos))
                    continue; // Skip this position but keep checking below

                // If position is valid and empty, gem can fall here
                lowestY = checkY;
            }

            // If gem can fall, add the move
            if (lowestY < y)
            {
                var fallToPos = new Vector3Int(x, lowestY, 0);
                movesList.Add(gemPos);    // from
                movesList.Add(fallToPos); // to
            }
        }
    }

    private GemMove GetGemMoveFromList(List<Vector3Int> movesList, int index)
    {
        // Extract move data from the packed list
        int baseIndex = index * 2;
        if (baseIndex + 1 < movesList.Count)
        {
            return new GemMove
            {
                from = movesList[baseIndex],
                to = movesList[baseIndex + 1]
            };
        }
        return new GemMove { from = Vector3Int.zero, to = Vector3Int.zero };
    }

    // ULTRA-FAST OPTIMIZATION: Helper method for column refill processing
    private void ProcessColumnRefillDirect(int x, (int xMin, int xMax, int yMin, int yMax) bounds, HashSet<Vector3Int> allEmptyPositions,
                                         ref int gemsActuallyCreated, ref bool wasChanged, int startYBase)
    {
        // Simple refill implementation - create gems for empty positions in this column
        var columnEmptyPositions = new List<Vector3Int>(8); // Individual list for this column

        foreach (var pos in allEmptyPositions)
        {
            if (pos.x == x && !gems.ContainsKey(pos) && CanGemFallHere(pos))
            {
                columnEmptyPositions.Add(pos);
            }
        }

        // Sort by Y for proper spawning order
        columnEmptyPositions.Sort((a, b) => a.y.CompareTo(b.y));


        for (int i = 0; i < columnEmptyPositions.Count; i++)
        {
            var pos = columnEmptyPositions[i];
            if (!gems.ContainsKey(pos)) // Double-check
            {
                // FIXED: Spawn gem just above the board, not way outside
                int spawnY = startYBase + 1; // Just one position above the board

                var newGem = CreateNewGem(pos, spawnY);
                if (newGem != null)
                {
                    gems[pos] = newGem;
                    gemsActuallyCreated++;
                    wasChanged = true;
                }
            }
        }
    }

    // ULTRA-FAST OPTIMIZATION: Helper method to check if a gem can fall to a position
    public bool CanGemFallHere(Vector3Int position)
    {
        return validPositions.Contains(position) &&
               !IsCellBlockedByObstacle(position) &&
               !IsGemHiddenByIce(position);
    }
}

public struct HintMove
{
    public Vector3Int startPos;
    public Vector3Int endPos;
    public bool found;
}

[BurstCompile]
public struct FindHintJob : IJob
{
    [ReadOnly] public NativeArray<Vector3Int> GemPositions;
    [ReadOnly] public NativeHashMap<Vector3Int, int> GemGrid;
    [ReadOnly] public NativeHashMap<Vector3Int, bool> UnswappableGrid;
    public NativeArray<HintMove> Result;

    public void Execute()
    {
        Result[0] = new HintMove { found = false };
        
        for (int i = 0; i < GemPositions.Length; i++)
        {
            var pos1 = GemPositions[i];
            if (UnswappableGrid.TryGetValue(pos1, out bool isUnswappable) && isUnswappable) continue;

            if (CheckSwap(pos1, pos1 + Vector3Int.right) || CheckSwap(pos1, pos1 + Vector3Int.up))
            {
                return;
            }
        }
    }

    private bool CheckSwap(Vector3Int pos1, Vector3Int pos2)
    {
        if (!GemGrid.ContainsKey(pos2) || (UnswappableGrid.TryGetValue(pos2, out bool isUnswappable) && isUnswappable))
        {
            return false;
        }

        // CRITICAL FIX: Use the same match detection logic as the actual swap system
        // This ensures hints only suggest moves that will actually work in the game

        // We need to check if both positions have gems and can be swapped
        if (!GemGrid.ContainsKey(pos1) || !GemGrid.ContainsKey(pos2))
        {
            return false;
        }

        // Check if either position is unswappable (Ice/IceAdjacentBreakable obstacles)
        bool pos1Unswappable = UnswappableGrid.TryGetValue(pos1, out bool unswap1) && unswap1;
        bool pos2Unswappable = UnswappableGrid.TryGetValue(pos2, out bool unswap2) && unswap2;

        if (pos1Unswappable || pos2Unswappable)
        {
            return false;
        }

        // Use simplified match detection for hint system performance
        var tempGrid = new NativeHashMap<Vector3Int, int>(GemGrid.Count, Allocator.Temp);
        var keys = GemGrid.GetKeyArray(Allocator.Temp);
        for(int i = 0; i < keys.Length; i++)
        {
            tempGrid.Add(keys[i], GemGrid[keys[i]]);
        }
        keys.Dispose();

        int type1 = tempGrid[pos1];
        int type2 = tempGrid[pos2];
        tempGrid[pos1] = type2;
        tempGrid[pos2] = type1;

        bool matchFound = CheckMatchAt(pos1, tempGrid) || CheckMatchAt(pos2, tempGrid);

        tempGrid.Dispose();

        if (matchFound)
        {
            Result[0] = new HintMove { startPos = pos1, endPos = pos2, found = true };
            return true;
        }
        return false;
    }

    private bool CheckMatchAt(Vector3Int pos, NativeHashMap<Vector3Int, int> grid)
    {
        if (!grid.TryGetValue(pos, out int type)) return false;

        int hCount = 1 + CountMatchesInDirection(pos, new Vector3Int(1, 0, 0), grid, type) + CountMatchesInDirection(pos, new Vector3Int(-1, 0, 0), grid, type);
        if (hCount >= 3) return true;

        int vCount = 1 + CountMatchesInDirection(pos, new Vector3Int(0, 1, 0), grid, type) + CountMatchesInDirection(pos, new Vector3Int(0, -1, 0), grid, type);
        return vCount >= 3;
    }

    private int CountMatchesInDirection(Vector3Int startPos, Vector3Int dir, NativeHashMap<Vector3Int, int> grid, int type)
    {
        int count = 0;
        for (int i = 1; i < 3; i++)
        {
            if (grid.TryGetValue(startPos + dir * i, out int neighborType) && neighborType == type)
                count++;
            else
                break;
        }
        return count;
    }
}
